package com.yqn.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yqn.common.tools.MessageTools;
import com.yqn.pojo.RechargeRecord;
import com.yqn.service.RechargeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 充值记录控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/recharge")
public class RechargeController {
    
    @Autowired
    private RechargeRecordService rechargeRecordService;
    
    @Autowired
    private MessageTools messageTools;
    
    /**
     * 获取用户充值记录
     */
    @GetMapping("/records")
    public Map<String, Object> getUserRechargeRecords(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        try {
            PageHelper.startPage(page, size);
            
            QueryWrapper<RechargeRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId)
                   .orderByDesc("create_time");
            
            List<RechargeRecord> records = rechargeRecordService.list(wrapper);
            PageInfo<RechargeRecord> pageInfo = new PageInfo<>(records);
            
            return messageTools.message(true, "获取充值记录成功", "data", pageInfo);
            
        } catch (Exception e) {
            log.error("获取充值记录失败", e);
            return messageTools.message(false, "获取充值记录失败：" + e.getMessage(), "", null);
        }
    }
    
    /**
     * 获取所有充值记录（管理员）
     */
    @GetMapping("/all")
    public Map<String, Object> getAllRechargeRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String orderNo) {
        
        try {
            PageHelper.startPage(page, size);
            
            QueryWrapper<RechargeRecord> wrapper = new QueryWrapper<>();
            
            if (status != null) {
                wrapper.eq("status", status);
            }
            
            if (orderNo != null && !orderNo.trim().isEmpty()) {
                wrapper.like("order_no", orderNo);
            }
            
            wrapper.orderByDesc("create_time");
            
            List<RechargeRecord> records = rechargeRecordService.list(wrapper);
            PageInfo<RechargeRecord> pageInfo = new PageInfo<>(records);
            
            return messageTools.message(true, "获取充值记录成功", "data", pageInfo);
            
        } catch (Exception e) {
            log.error("获取充值记录失败", e);
            return messageTools.message(false, "获取充值记录失败：" + e.getMessage(), "", null);
        }
    }
    
    /**
     * 根据订单号查询充值记录
     */
    @GetMapping("/order/{orderNo}")
    public Map<String, Object> getRechargeRecordByOrderNo(@PathVariable String orderNo) {
        try {
            RechargeRecord record = rechargeRecordService.getByOrderNo(orderNo);
            
            if (record != null) {
                return messageTools.message(true, "查询成功", "data", record);
            } else {
                return messageTools.message(false, "充值记录不存在", "", null);
            }
            
        } catch (Exception e) {
            log.error("查询充值记录失败", e);
            return messageTools.message(false, "查询失败：" + e.getMessage(), "", null);
        }
    }
}
