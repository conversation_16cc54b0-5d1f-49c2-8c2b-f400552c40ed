package com.yqn.controller;

import com.yqn.common.tools.MessageTools;
import com.yqn.dto.AlipayDTO;
import com.yqn.service.AlipayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.UUID;

/**
 * 支付宝支付控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/alipay")
public class AlipayController {
    
    @Autowired
    private AlipayService alipayService;
    
    @Autowired
    private MessageTools messageTools;
    
    /**
     * 创建支付订单
     */
    @PostMapping("/pay")
    public void createPayment(@RequestBody AlipayDTO alipayDTO, HttpServletResponse response) {
        try {
            // 生成订单号
            String orderNo = "RECHARGE_" + System.currentTimeMillis() + "_" + 
                           UUID.randomUUID().toString().replace("-", "").substring(0, 8);
            alipayDTO.setOrderNo(orderNo);
            
            // 设置默认订单名称
            if (alipayDTO.getSubject() == null || alipayDTO.getSubject().trim().isEmpty()) {
                alipayDTO.setSubject("校园帮账户充值");
            }
            
            // 创建支付订单
            String paymentForm = alipayService.createPayment(alipayDTO);
            
            // 直接返回支付表单HTML
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write(paymentForm);
            response.getWriter().flush();
            
        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            try {
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"success\":false,\"message\":\"" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("返回错误信息失败", ioException);
            }
        }
    }
    
    /**
     * 支付宝异步通知
     */
    @PostMapping("/notify")
    public String handleNotify(HttpServletRequest request) {
        log.info("收到支付宝异步通知");
        return alipayService.handleNotify(request);
    }
    
    /**
     * 支付宝同步返回
     */
    @GetMapping("/return")
    public Map<String, Object> handleReturn(HttpServletRequest request) {
        log.info("收到支付宝同步返回");
        Map<String, Object> result = alipayService.handleReturn(request);
        
        if ((Boolean) result.get("success")) {
            return messageTools.message(true, "支付成功", "data", result);
        } else {
            return messageTools.message(false, (String) result.get("message"), "", null);
        }
    }
    
    /**
     * 查询支付状态
     */
    @GetMapping("/query/{orderNo}")
    public Map<String, Object> queryPaymentStatus(@PathVariable String orderNo) {
        try {
            Map<String, Object> result = alipayService.queryPaymentStatus(orderNo);
            
            if ((Boolean) result.get("success")) {
                return messageTools.message(true, "查询成功", "data", result);
            } else {
                return messageTools.message(false, (String) result.get("message"), "", null);
            }
            
        } catch (Exception e) {
            log.error("查询支付状态失败", e);
            return messageTools.message(false, "查询失败：" + e.getMessage(), "", null);
        }
    }
    
    /**
     * 获取支付配置信息（用于前端显示）
     */
    @GetMapping("/config")
    public Map<String, Object> getPaymentConfig() {
        Map<String, Object> config = Map.of(
            "minAmount", 0.01,
            "maxAmount", 10000.0,
            "supportedMethods", new String[]{"alipay"}
        );
        return messageTools.message(true, "获取配置成功", "config", config);
    }
}
