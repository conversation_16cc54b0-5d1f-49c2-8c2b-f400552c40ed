package com.yqn.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 充值记录实体类
 * <AUTHOR>
 */
@Data
@ToString
@TableName("recharge_record")
public class RechargeRecord {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;
    
    /**
     * 支付宝交易号
     */
    @TableField("trade_no")
    private String tradeNo;
    
    /**
     * 充值金额
     */
    @TableField("amount")
    private Double amount;
    
    /**
     * 支付状态：0-待支付，1-支付成功，2-支付失败，3-已取消
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 支付方式：alipay-支付宝
     */
    @TableField("payment_method")
    private String paymentMethod;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    
    /**
     * 支付时间
     */
    @TableField("pay_time")
    private Date payTime;
    
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    
    /**
     * 用户信息（关联查询）
     */
    @TableField(exist = false)
    private User user;
}
