{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue", "mtime": 1748631905674}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7bWFwU3RhdGUsIG1hcE11dGF0aW9uc30gZnJvbSAidnVleCIKaW1wb3J0IHVzZXIgZnJvbSAiQHMvc3RvcmUvbW9kdWxlL3VzZXIiOwppbXBvcnQgcmVnaW9uRGF0YSBmcm9tICdAL2Fzc2V0cy9kYXRhL3JlZ2lvbi5qcyc7CgpleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAiSG9tZSIsCiAgICBtZXRob2RzOiB7CiAgICAgICAgLi4ubWFwTXV0YXRpb25zKCd1c2VyJywgWydzZXRVc2VyJ10pLAogICAgICAgIGNoYW5nZUNvbG9yKHZhbCl7CiAgICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oInRoZW1lQ29sb3IiLEpTT04uc3RyaW5naWZ5KHZhbCkpCiAgICAgICAgICAgIHRoaXMudGhlbWVDb2xvciA9IHsnYmcnOnZhbC52YWx1ZSwnY29sb3InOnZhbC5jb2xvcn07CiAgICAgICAgfSwKICAgICAgICAvL+mdouWMheWxkQogICAgICAgIGdldEJyZWFkY3J1bWIoKSB7CiAgICAgICAgICAgIGxldCBtYXRjaGVkID0gdGhpcy4kcm91dGUubWF0Y2hlZDsKICAgICAgICAgICAgaWYgKG1hdGNoZWRbMF0ubmFtZSAhPSAnaG9tZScpIHsKICAgICAgICAgICAgICAgIG1hdGNoZWQgPSBbe3BhdGg6ICIvaG9tZS8iLCBtZXRhOiB7dGl0bGU6ICfpppbpobUnfX1dLmNvbmNhdChtYXRjaGVkKQogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuYnJlYWRMaXN0ID0gbWF0Y2hlZDsKICAgICAgICB9LAogICAgICAgIC8v5YWz6Zet5oq95bGJ6Kem5Y+R55qE5LqL5Lu2CiAgICAgICAgaGFuZGxlQ2xvc2UoZG9uZSkgewogICAgICAgICAgICAvLyDlpoLmnpzmmK/pppbmrKHnmbvlvZXkuJTmnKrlrozlloTkv6Hmga/vvIzkuI3lhYHorrjlhbPpl63mir3lsYkKICAgICAgICAgICAgaWYgKHRoaXMudXNlci5zdGF0ZSA9PT0gMCkgewogICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLpppbmrKHnmbvlvZXlv4XpobvlrozlloTkv6Hmga8iLCAiZXJyb3IiKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgLy8g5aaC5p6c5LiN5piv6aaW5qyh55m75b2V5oiW5bey5a6M5ZaE5L+h5oGv77yM5YWB6K645YWz6Zet5oq95bGJCiAgICAgICAgICAgICAgICBkb25lKCkKICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc3VibWl0Rm9ybShmb3JtTmFtZSkgewogICAgICAgICAgICB0aGlzLiRyZWZzW2Zvcm1OYW1lXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICAgICAgICAgIC8vIOagueaNruinkuiJsklE5Yaz5a6a5piv5ZCm6ZyA6KaB6YCJ5oup57G75YirCiAgICAgICAgICAgICAgICAgICAgaWYgKCh0aGlzLnVzZXIucm9sZS5pZCAhPT0gMTQgJiYgdGhpcy52YWx1ZSkgfHwgdGhpcy51c2VyLnJvbGUuaWQgPT09IDE0KSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHRoaXMudXNlci5pZCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlcHRJZDogdGhpcy51c2VyLnJvbGUuaWQgIT09IDE0ID8gdGhpcy52YWx1ZVswXSA6IG51bGwsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBjbGFzc0lkIOWtl+auteWcqOaVsOaNruW6k+S4reS4jeWtmOWcqO+8jOenu+mZpOivpeWtl+autQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlcm5hbWU6IHRoaXMucnVsZUZvcm0udXNlcm5hbWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwaG9uZTogdGhpcy5ydWxlRm9ybS5waG9uZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNleDogdGhpcy5zZXgsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDorr7nva7nirbmgIHkuLrlt7LlrozlloTkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRlOiAxCiAgICAgICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHB1dCgiL3VzZXIiLCB1c2VyRGF0YSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0YXR1cykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmRyYXdlciA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRub3RpZnlNc2coIuaIkOWKnyIsIHJlcy5kYXRhLm1zZywgInN1Y2Nlc3MiKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmm7TmlrDmnKzlnLDnlKjmiLfkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnVzZXIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ3VzZXInLCBKU09OLnN0cmluZ2lmeShyZXMuZGF0YS51c2VyKSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0VXNlcihyZXMuZGF0YS51c2VyKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCh0aGlzLnVzZXIuaWQpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKHJlcy5kYXRhLm1zZywgImVycm9yIikKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignVXBkYXRlIGZhaWxlZDonLCBlcnIpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKGVyci5yZXNwb25zZT8uZGF0YT8ubXNnIHx8ICLmm7TmlrDlpLHotKXvvIzor7fnqI3lkI7ph43or5UiLCAiZXJyb3IiKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRub3RpZnlNc2coIumUmeivryIsICLor7fpgInmi6nnsbvliKsiLCAiZXJyb3IiKQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIC8v5L+u5pS55a+G56CBCiAgICAgICAgdXBkUGFzc3dvcmQoaWQpIHsKICAgICAgICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXlr4bnoIEnLCAn5o+Q56S6JywgewogICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgICAgICBpbnB1dFR5cGU6ICdwYXNzd29yZCcsCiAgICAgICAgICAgICAgICBjbG9zZU9uQ2xpY2tNb2RhbDpmYWxzZSwKICAgICAgICAgICAgICAgIGlucHV0UGF0dGVybjogL14oPyFbMC05XSskKSg/IVthLXpBLVpdKyQpWzAtOUEtWmEtel17NiwxNn0kLywKICAgICAgICAgICAgICAgIGlucHV0RXJyb3JNZXNzYWdlOiAn5qC85byP5LiN5a+577yM5a+G56CB5Y+q6IO96L6T5YWlNi0xNuS9jeiLseaWh+WSjOaVsOWtlycKICAgICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAvLyBjb25zb2xlLmxvZyhyZXMpOwogICAgICAgICAgICAgICAgdGhpcy4kcHV0KCcvdXNlcicsIHtpZDogaWQsIHBhc3N3b3JkOiByZXMudmFsdWV9KQogICAgICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG5vdGlmeU1zZygn5oiQ5YqfJywgcmVzLmRhdGEubXNnLCAnc3VjY2VzcycpCiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIH0pCiAgICAgICAgfSwKICAgICAgICByZWNoYXJnZShzdHVkZW50SWQpIHsKICAgICAgICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXlhYXlgLzph5Hpop0nLCAn5YWF5YC8JywgewogICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgICAgICBpbnB1dFBhdHRlcm46IC9eKDBcLlxkezEsMn18WzEtOV1cZCooXC5cZHsxLDJ9KT8pJC8sCiAgICAgICAgICAgICAgICBpbnB1dEVycm9yTWVzc2FnZTogJ+ivt+i+k+WFpeacieaViOmHkemine+8iOacgOWkmuS4pOS9jeWwj+aVsO+8iScKICAgICAgICAgICAgfSkudGhlbigoeyB2YWx1ZSB9KSA9PiB7CiAgICAgICAgICAgICAgICBjb25zdCBhbW91bnQgPSBwYXJzZUZsb2F0KHZhbHVlKTsKCiAgICAgICAgICAgICAgICAvLyDpqozor4Hph5Hpop3ojIPlm7QKICAgICAgICAgICAgICAgIGlmIChhbW91bnQgPCAwLjAxIHx8IGFtb3VudCA+IDEwMDAwKSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLlhYXlgLzph5Hpop3lv4XpobvlnKgwLjAxLTEwMDAw5YWD5LmL6Ze0IiwgImVycm9yIik7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIC8vIOWIm+W7uuaUr+S7mOWuneaUr+S7mOiuouWNlQogICAgICAgICAgICAgICAgY29uc3QgcGF5RGF0YSA9IHsKICAgICAgICAgICAgICAgICAgICBzdWJqZWN0OiAn5qCh5Zut5biu6LSm5oi35YWF5YC8JywKICAgICAgICAgICAgICAgICAgICB0b3RhbEFtb3VudDogYW1vdW50LAogICAgICAgICAgICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VyLmlkCiAgICAgICAgICAgICAgICB9OwoKICAgICAgICAgICAgICAgIC8vIOiwg+eUqOWQjuerr+WIm+W7uuaUr+S7mOiuouWNleaOpeWPowogICAgICAgICAgICAgICAgdGhpcy4kcG9zdCgnYXBpL2FsaXBheS9jcmVhdGUnLCBwYXlEYXRhKQogICAgICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWcqOaWsOeql+WPo+S4reaJk+W8gOaUr+S7mOmhtemdogogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcGF5V2luZG93ID0gd2luZG93Lm9wZW4oJycsICdfYmxhbmsnLCAnd2lkdGg9ODAwLGhlaWdodD02MDAnKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBheVdpbmRvdy5kb2N1bWVudC53cml0ZShyZXMuZGF0YS5wYXlGb3JtKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBheVdpbmRvdy5kb2N1bWVudC5jbG9zZSgpOwoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOebkeWQrOaUr+S7mOeql+WPo+WFs+mXre+8jOWIt+aWsOeUqOaIt+S/oeaBrwogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY2hlY2tDbG9zZWQgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBheVdpbmRvdy5jbG9zZWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja0Nsb3NlZCk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWIt+aWsOeUqOaIt+S/oeaBrwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm5ld0xpc3QodGhpcy51c2VyLmlkKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLor7fmo4Dmn6XmlK/ku5jnu5PmnpwiLCAiaW5mbyIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sIDEwMDApOwogICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKHJlcy5kYXRhLm1zZyB8fCAi5Yib5bu65pSv5LuY6K6i5Y2V5aSx6LSlIiwgImVycm9yIik7CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgIC5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliJvlu7rmlK/ku5jorqLljZXlpLHotKU6JywgZXJyKTsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLliJvlu7rmlK/ku5jorqLljZXlpLHotKXvvIzor7fnqI3lkI7ph43or5UiLCAiZXJyb3IiKTsKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLlt7Llj5bmtojlhYXlgLwiLCAiaW5mbyIpOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIHBlcnNvbmFsSW5mb3JtYXRpb24oKSB7CiAgICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgICAgICAgIHRoaXMucnVsZUZvcm0udXNlcm5hbWUgPSB0aGlzLnVzZXIudXNlcm5hbWU7CiAgICAgICAgICAgIHRoaXMucnVsZUZvcm0ucGhvbmUgPSB0aGlzLnVzZXIucGhvbmU7CgogICAgICAgICAgICAvLyDlpoLmnpzmmK/nu7Tkv67lkZjvvIzliqDovb3nsbvliKvlkozlrZDnsbvliKvmlbDmja4KICAgICAgICAgICAgaWYgKHRoaXMudXNlci5yb2xlICYmIHRoaXMudXNlci5yb2xlLmlkID09PSAxMykgewogICAgICAgICAgICAgICAgLy8g6K6+572u5b2T5YmN55qE57G75Yir5ZKM5a2Q57G75YirCiAgICAgICAgICAgICAgICBpZiAodGhpcy51c2VyLmRlcHQpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmRlcHRJZCA9IHRoaXMudXNlci5kZXB0LmlkOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKHRoaXMudXNlci50eXBlKSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5jbGFzc0lkID0gdGhpcy51c2VyLnR5cGUuaWQ7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgLy8g6K6+572u57qn6IGU6YCJ5oup5Zmo55qE5Yid5aeL5YC8CiAgICAgICAgICAgICAgICBpZiAodGhpcy51c2VyLmRlcHQgJiYgdGhpcy51c2VyLnR5cGUpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmNhdGVnb3J5VmFsdWUgPSBbdGhpcy51c2VyLmRlcHQuaWQsIHRoaXMudXNlci50eXBlLmlkXTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy51c2VyLmRlcHQpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmNhdGVnb3J5VmFsdWUgPSBbdGhpcy51c2VyLmRlcHQuaWRdOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmNhdGVnb3J5VmFsdWUgPSBbXTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAvLyDorr7nva7lnLDlnYDpgInmi6nlmajnmoTliJ3lp4vlgLwKICAgICAgICAgICAgICAgIGlmICh0aGlzLnVzZXIucHJvdmluY2UgJiYgdGhpcy51c2VyLmNpdHkgJiYgdGhpcy51c2VyLmRpc3RyaWN0KSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5hZGRyZXNzUmVnaW9uID0gW3RoaXMudXNlci5wcm92aW5jZSwgdGhpcy51c2VyLmNpdHksIHRoaXMudXNlci5kaXN0cmljdF07CiAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5hZGRyZXNzID0gdGhpcy51c2VyLmFkZHJlc3MgfHwgJyc7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIHRoaXMuYWRkcmVzc1JlZ2lvbiA9IFtdOwogICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uYWRkcmVzcyA9ICcnOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIC8vIOWKoOi9veaJgOacieWPr+eUqOeahOexu+WIq+WSjOWtkOexu+WIqwogICAgICAgICAgICAgICAgdGhpcy5sb2FkQ2F0ZWdvcnlPcHRpb25zKCk7CiAgICAgICAgICAgIH0KICAgICAgICB9LAoKICAgICAgICAvLyDliqDovb3nsbvliKvlkozlrZDnsbvliKvnmoTnuqfogZTpgInpobkKICAgICAgICBsb2FkQ2F0ZWdvcnlPcHRpb25zKCkgewogICAgICAgICAgICAvLyDlhYjojrflj5bmiYDmnInnsbvliKsKICAgICAgICAgICAgdGhpcy4kZ2V0KCIvZGVwdC9saXN0IiwgeyByb2xlSWQ6IDEzIH0pCiAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuc3RhdHVzICYmIHJlcy5kYXRhLmRlcHQpIHsKICAgICAgICAgICAgICAgICAgICAvLyDkv53lrZjljp/lp4vnsbvliKvliJfooajvvIjlhbzlrrnmgKfogIPomZHvvIkKICAgICAgICAgICAgICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gcmVzLmRhdGEuZGVwdDsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5Y+v55So55qE57G75Yir5YiX6KGoOicsIHRoaXMuZGVwdE9wdGlvbnMpOwoKICAgICAgICAgICAgICAgICAgICAvLyDkuLrmr4/kuKrnsbvliKvliqDovb3lrZDnsbvliKsKICAgICAgICAgICAgICAgICAgICBjb25zdCBwcm9taXNlcyA9IHJlcy5kYXRhLmRlcHQubWFwKGRlcHQgPT4gewogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy4kZ2V0KCIvY2xhc3MvbGlzdCIsIHsgZGVwdElkOiBkZXB0LmlkIH0pCiAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKGNsYXNzUmVzID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjbGFzc1Jlcy5kYXRhLnN0YXR1cyAmJiBjbGFzc1Jlcy5kYXRhLmNsYXNzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g6L+U5Zue5bim5pyJ5a2Q57G75Yir55qE57G75Yir5a+56LGhCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZGVwdCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IGNsYXNzUmVzLmRhdGEuY2xhc3MKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5a2Q57G75Yir77yM6L+U5Zue5Y6f5aeL57G75Yir5a+56LGhCiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmRlcHQsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IFtdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5Ye66ZSZ5pe26L+U5Zue5rKh5pyJ5a2Q57G75Yir55qE57G75Yir5a+56LGhCiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmRlcHQsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IFtdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgICAgICAgLy8g562J5b6F5omA5pyJ5a2Q57G75Yir5Yqg6L295a6M5oiQCiAgICAgICAgICAgICAgICAgICAgUHJvbWlzZS5hbGwocHJvbWlzZXMpCiAgICAgICAgICAgICAgICAgICAgLnRoZW4oY2F0ZWdvcmllcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY2F0ZWdvcnlPcHRpb25zID0gY2F0ZWdvcmllczsKICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+e6p+iBlOmAieaLqeWZqOmAiemhuTonLCB0aGlzLmNhdGVnb3J5T3B0aW9ucyk7CiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi6I635Y+W57G75Yir5YiX6KGo5aSx6LSlIiwgImVycm9yIik7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIC5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W57G75Yir5YiX6KGo5aSx6LSlOicsIGVycik7CiAgICAgICAgICAgICAgICB0aGlzLiRtc2coIuiOt+WPluexu+WIq+WIl+ihqOWksei0pe+8jOivt+eojeWQjumHjeivlSIsICJlcnJvciIpOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAoKICAgICAgICAvLyDkv53nlZnljp/mnInnmoTmlrnms5XvvIjlhbzlrrnmgKfogIPomZHvvIkKICAgICAgICBsb2FkRGVwYXJ0bWVudHMoKSB7CiAgICAgICAgICAgIHRoaXMuJGdldCgiL2RlcHQvbGlzdCIsIHsgcm9sZUlkOiAxMyB9KQogICAgICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0YXR1cyAmJiByZXMuZGF0YS5kZXB0KSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5kZXB0T3B0aW9ucyA9IHJlcy5kYXRhLmRlcHQ7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+WPr+eUqOeahOexu+WIq+WIl+ihqDonLCB0aGlzLmRlcHRPcHRpb25zKTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLojrflj5bnsbvliKvliJfooajlpLHotKUiLCAiZXJyb3IiKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnsbvliKvliJfooajlpLHotKU6JywgZXJyKTsKICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi6I635Y+W57G75Yir5YiX6KGo5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIiwgImVycm9yIik7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0sCgogICAgICAgIC8vIOS/neeVmeWOn+acieeahOaWueazle+8iOWFvOWuueaAp+iAg+iZke+8iQogICAgICAgIGxvYWRTdWJDYXRlZ29yaWVzKGRlcHRJZCkgewogICAgICAgICAgICBpZiAoIWRlcHRJZCkgcmV0dXJuOwoKICAgICAgICAgICAgdGhpcy4kZ2V0KCIvY2xhc3MvbGlzdCIsIHsgZGVwdElkOiBkZXB0SWQgfSkKICAgICAgICAgICAgLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMgJiYgcmVzLmRhdGEuY2xhc3MpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmNsYXNzT3B0aW9ucyA9IHJlcy5kYXRhLmNsYXNzOwogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflj6/nlKjnmoTlrZDnsbvliKvliJfooag6JywgdGhpcy5jbGFzc09wdGlvbnMpOwoKICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzlvZPliY3pgInmi6nnmoTlrZDnsbvliKvkuI3lnKjmlrDnmoTlrZDnsbvliKvliJfooajkuK3vvIzmuIXnqbrpgInmi6kKICAgICAgICAgICAgICAgICAgICBjb25zdCBleGlzdHMgPSB0aGlzLmNsYXNzT3B0aW9ucy5zb21lKGl0ZW0gPT4gaXRlbS5pZCA9PT0gdGhpcy5ydWxlRm9ybS5jbGFzc0lkKTsKICAgICAgICAgICAgICAgICAgICBpZiAoIWV4aXN0cykgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmNsYXNzSWQgPSBudWxsOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGFzc09wdGlvbnMgPSBbXTsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmNsYXNzSWQgPSBudWxsOwogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi6I635Y+W5a2Q57G75Yir5YiX6KGo5aSx6LSlIiwgImVycm9yIik7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIC5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a2Q57G75Yir5YiX6KGo5aSx6LSlOicsIGVycik7CiAgICAgICAgICAgICAgICB0aGlzLmNsYXNzT3B0aW9ucyA9IFtdOwogICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5jbGFzc0lkID0gbnVsbDsKICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi6I635Y+W5a2Q57G75Yir5YiX6KGo5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIiwgImVycm9yIik7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0sCiAgICAgICAgLy8gc3VibWl0Q2hhbmdlcygpewogICAgICAgIC8vICAgICB0aGlzLiRwdXQoIi91c2VyIix7aWQ6dGhpcy51c2VyLmlkLHVzZXJuYW1lOnRoaXMucnVsZUZvcm0udXNlcm5hbWUscGhvbmU6dGhpcy5ydWxlRm9ybS5waG9uZX0pCiAgICAgICAgLy8gICAgIC50aGVuKHJlcz0+ewogICAgICAgIC8vICAgICAgICAgdGhpcy4kbm90aWZ5TXNnKCLmiJDlip8iLHJlcy5kYXRhLm1zZywic3VjY2VzcyIsMTAwMCk7CiAgICAgICAgLy8gICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAvLyAgICAgICAgIHRoaXMubmV3TGlzdCh0aGlzLnVzZXIuaWQpCiAgICAgICAgLy8gICAgIH0pCiAgICAgICAgLy8gfSwKICAgICAgICBzdWJtaXRDaGFuZ2VzKCkgewogICAgICAgICAgICAvLyDlh4blpIfopoHmm7TmlrDnmoTnlKjmiLfmlbDmja4KICAgICAgICAgICAgY29uc3QgdXNlckRhdGEgPSB7CiAgICAgICAgICAgICAgICBpZDogdGhpcy51c2VyLmlkLAogICAgICAgICAgICAgICAgdXNlcm5hbWU6IHRoaXMucnVsZUZvcm0udXNlcm5hbWUsCiAgICAgICAgICAgICAgICBwaG9uZTogdGhpcy5ydWxlRm9ybS5waG9uZSwKICAgICAgICAgICAgICAgIHN0YXRlOiAxCiAgICAgICAgICAgIH07CgogICAgICAgICAgICAvLyDlpoLmnpzmmK/nu7Tkv67lkZjvvIzmt7vliqDnsbvliKvlkozlrZDnsbvliKvkv6Hmga8KICAgICAgICAgICAgaWYgKHRoaXMudXNlci5yb2xlICYmIHRoaXMudXNlci5yb2xlLmlkID09PSAxMykgewogICAgICAgICAgICAgICAgLy8g5LuO57qn6IGU6YCJ5oup5Zmo5Lit6I635Y+W57G75Yir5ZKM5a2Q57G75YirSUQKICAgICAgICAgICAgICAgIGlmICh0aGlzLmNhdGVnb3J5VmFsdWUgJiYgdGhpcy5jYXRlZ29yeVZhbHVlLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICAgICAgICAvLyDnrKzkuIDkuKrlgLzmmK/nsbvliKtJRAogICAgICAgICAgICAgICAgICAgIHVzZXJEYXRhLmRlcHRJZCA9IHRoaXMuY2F0ZWdvcnlWYWx1ZVswXTsKCiAgICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c6YCJ5oup5LqG5a2Q57G75Yir77yI5pWw57uE6ZW/5bqm5aSn5LqOMe+8ie+8jOesrOS6jOS4quWAvOaYr+WtkOexu+WIq0lECiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuY2F0ZWdvcnlWYWx1ZS5sZW5ndGggPiAxKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJEYXRhLmNsYXNzSWQgPSB0aGlzLmNhdGVnb3J5VmFsdWVbMV07CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgLy8g5YW85a655pen54mI5pys55qE6YCJ5oup5pa55byPCiAgICAgICAgICAgICAgICBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAvLyDlj6rmnInlvZPpgInmi6nkuobnsbvliKvml7bmiY3mm7TmlrDnsbvliKtJRAogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLmRlcHRJZCkgewogICAgICAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5kZXB0SWQgPSB0aGlzLnJ1bGVGb3JtLmRlcHRJZDsKICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgIC8vIOWPquacieW9k+mAieaLqeS6huWtkOexu+WIq+aXtuaJjeabtOaWsOWtkOexu+WIq0lECiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMucnVsZUZvcm0uY2xhc3NJZCkgewogICAgICAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5jbGFzc0lkID0gdGhpcy5ydWxlRm9ybS5jbGFzc0lkOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAvLyDlpITnkIblnLDlnYDkv6Hmga8KICAgICAgICAgICAgICAgIGlmICh0aGlzLmFkZHJlc3NSZWdpb24gJiYgdGhpcy5hZGRyZXNzUmVnaW9uLmxlbmd0aCA9PT0gMykgewogICAgICAgICAgICAgICAgICAgIHVzZXJEYXRhLnByb3ZpbmNlID0gdGhpcy5hZGRyZXNzUmVnaW9uWzBdOwogICAgICAgICAgICAgICAgICAgIHVzZXJEYXRhLmNpdHkgPSB0aGlzLmFkZHJlc3NSZWdpb25bMV07CiAgICAgICAgICAgICAgICAgICAgdXNlckRhdGEuZGlzdHJpY3QgPSB0aGlzLmFkZHJlc3NSZWdpb25bMl07CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgLy8g5aaC5p6c5pyJ6K+m57uG5Zyw5Z2A77yM5re75Yqg5Yiw5pu05paw5pWw5o2u5LitCiAgICAgICAgICAgICAgICBpZiAodGhpcy5ydWxlRm9ybS5hZGRyZXNzKSB7CiAgICAgICAgICAgICAgICAgICAgdXNlckRhdGEuYWRkcmVzcyA9IHRoaXMucnVsZUZvcm0uYWRkcmVzczsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQoKICAgICAgICAgICAgY29uc29sZS5sb2coJ+aPkOS6pOeahOeUqOaIt+aVsOaNrjonLCB1c2VyRGF0YSk7CgogICAgICAgICAgICAvLyDlj5HpgIHmm7TmlrDor7fmsYIKICAgICAgICAgICAgdGhpcy4kcHV0KCIvdXNlciIsIHVzZXJEYXRhKQogICAgICAgICAgICAgICAgLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuc3RhdHVzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG5vdGlmeU1zZygi5oiQ5YqfIiwgcmVzLmRhdGEubXNnLCAic3VjY2VzcyIpOwogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5uZXdMaXN0KHRoaXMudXNlci5pZCk7CiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKHJlcy5kYXRhLm1zZyB8fCAi5pu05paw5aSx6LSlIiwgImVycm9yIik7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIC5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+abtOaWsOeUqOaIt+S/oeaBr+Wksei0pTonLCBlcnIpOwogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi5pu05paw5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIiwgImVycm9yIik7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIC8v5qC55o2u5b2T5YmN55So5oi35p+l6K+iaWQKICAgICAgICBuZXdMaXN0KGlkKSB7CiAgICAgICAgICAgIHRoaXMuJGdldCgiL3VzZXIvIiArIGlkKQogICAgICAgICAgICAudGhlbigocnMpID0+IHsKICAgICAgICAgICAgICAgIC8vIOehruS/neeUqOaIt+eKtuaAgeato+ehruabtOaWsAogICAgICAgICAgICAgICAgaWYgKHJzLmRhdGEudXNlcikgewogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIGRhdGEgZnJvbSBBUEk6JywgSlNPTi5zdHJpbmdpZnkocnMuZGF0YS51c2VyLCBudWxsLCAyKSk7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgcm9sZSBJRDonLCBycy5kYXRhLnVzZXIucm9sZSA/IHJzLmRhdGEudXNlci5yb2xlLmlkIDogJ05vIHJvbGUnKTsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVXNlciBkZXB0OicsIHJzLmRhdGEudXNlci5kZXB0ID8gSlNPTi5zdHJpbmdpZnkocnMuZGF0YS51c2VyLmRlcHQsIG51bGwsIDIpIDogJ05vIGRlcHQgZGF0YScpOwogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIHR5cGU6JywgcnMuZGF0YS51c2VyLnR5cGUgPyBKU09OLnN0cmluZ2lmeShycy5kYXRhLnVzZXIudHlwZSwgbnVsbCwgMikgOiAnTm8gdHlwZSBkYXRhJyk7CgogICAgICAgICAgICAgICAgICAgIC8vIOWmguaenOaYr+e7tOS/ruWRmOS9huayoeaciXR5cGXkv6Hmga/vvIzlsJ3or5Xph43mlrDojrflj5bnlKjmiLfmlbDmja4KICAgICAgICAgICAgICAgICAgICBpZiAocnMuZGF0YS51c2VyLnJvbGUgJiYgcnMuZGF0YS51c2VyLnJvbGUuaWQgPT09IDEzICYmICFycy5kYXRhLnVzZXIudHlwZSkgewogICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn57u05L+u5ZGY5rKh5pyJdHlwZeS/oeaBr++8jOWwneivleS/ruWkjS4uLicpOwogICAgICAgICAgICAgICAgICAgICAgICAvLyDlsJ3or5XmiYvliqjorr7nva50eXBl5L+h5oGvCiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChycy5kYXRhLnVzZXIuY2xhc3NJZCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+aJvuWIsGNsYXNzSWQ6JywgcnMuZGF0YS51c2VyLmNsYXNzSWQpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g6I635Y+WdHlwZeS/oeaBrwogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kZ2V0KCIvY2xhc3MvIiArIHJzLmRhdGEudXNlci5jbGFzc0lkKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLnRoZW4odHlwZVJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVSZXMuZGF0YS5jbGFzcykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn6I635Y+W5YiwdHlwZeS/oeaBrzonLCBKU09OLnN0cmluZ2lmeSh0eXBlUmVzLmRhdGEuY2xhc3MsIG51bGwsIDIpKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5omL5Yqo6K6+572udHlwZeS/oeaBrwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBycy5kYXRhLnVzZXIudHlwZSA9IHR5cGVSZXMuZGF0YS5jbGFzczsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5pu05paw55So5oi35L+h5oGvCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oInVzZXIiLCBKU09OLnN0cmluZ2lmeShycy5kYXRhLnVzZXIpKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZXRVc2VyKHJzLmRhdGEudXNlcik7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgIC8vIOWmguaenOeUqOaIt+W3suWujOWWhOS/oeaBr++8jOehruS/neeKtuaAgeS4ujEKICAgICAgICAgICAgICAgICAgICBpZiAocnMuZGF0YS51c2VyLmRlcHQgJiYgcnMuZGF0YS51c2VyLnN0YXRlID09PSAwKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHJzLmRhdGEudXNlci5zdGF0ZSA9IDE7CiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOabtOaWsOeUqOaIt+eKtuaAgQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRwdXQoIi91c2VyIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHJzLmRhdGEudXNlci5pZCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRlOiAxCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICBzZXNzaW9uU3RvcmFnZS5zZXRJdGVtKCJ1c2VyIiwgSlNPTi5zdHJpbmdpZnkocnMuZGF0YS51c2VyKSkKICAgICAgICAgICAgICAgICAgICB0aGlzLnNldFVzZXIoSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJ1c2VyIikpKQogICAgICAgICAgICAgICAgICAgIC8vIOS/ruaUueWujOWQjeWtlywg5riF56m655W25YmNZmlyc3ROYW1lOyDpgb/lhY3lh7rnj77nlorliqAKICAgICAgICAgICAgICAgICAgICB0aGlzLmZpcnN0TmFtZSA9ICcnOwogICAgICAgICAgICAgICAgICAgIHRoaXMudGV4dEF2YXRhcihycy5kYXRhLnVzZXIudXNlcm5hbWUpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgIH0sCiAgICAgICAgZXhpdCgpewogICAgICAgICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7CiAgICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvJykKICAgICAgICB9LAogICAgICAgIC8vIOaWh+Wtl+mgreWDjwogICAgICAgIHRleHRBdmF0YXIodXNlcm5hbWUpIHsKICAgICAgICAgICAgbGV0IGFyciA9IHVzZXJuYW1lLnNwbGl0KCcgJyk7CiAgICAgICAgICAgIGZvciAodmFyIGkgaW4gYXJyKSB7CiAgICAgICAgICAgICAgICB0aGlzLmZpcnN0TmFtZSArPSBhcnJbaV0uc3Vic3RyKDAsMSk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5maXJzdE5hbWUgPSB0aGlzLmZpcnN0TmFtZS50b0xvY2FsZVVwcGVyQ2FzZSgpOwogICAgICAgICAgICBjb25zb2xlLmxvZygnZmlyc3ROYW1lLT4nICsgdGhpcy5maXJzdE5hbWUpOwogICAgICAgIH0sCgogICAgICAgIC8vIOiOt+WPluacquivu+mAmuefpeaVsOmHjwogICAgICAgIGdldFVucmVhZE5vdGlmaWNhdGlvbkNvdW50KCkgewogICAgICAgICAgICBpZiAodGhpcy51c2VyICYmIHRoaXMudXNlci5pZCkgewogICAgICAgICAgICAgICAgdGhpcy4kZ2V0KCcvZm9ydW0vbm90aWZpY2F0aW9uL3VucmVhZC9jb3VudCcsIHsgdXNlcklkOiB0aGlzLnVzZXIuaWQgfSkKICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0YXR1cykgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnVucmVhZENvdW50ID0gcmVzLmRhdGEuY291bnQ7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIC5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluacquivu+mAmuefpeaVsOmHj+Wksei0pTonLCBlcnIpOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICB9CiAgICB9LAogICAgY29tcHV0ZWQ6IHsKICAgICAgICAuLi5tYXBTdGF0ZSgndXNlcicsIFsndXNlciddKSwKICAgICAgICB0aGVtZSgpewogICAgICAgICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUudGhlbWUudGhlbWUKICAgICAgICB9LAogICAgfSwKICAgIGRhdGEoKSB7CiAgICAgICAgdmFyIHZhbGlkYXRlVXNlcm5hbWUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgICAgICAgIGlmICh2YWx1ZSA9PT0gJycpIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5aeT5ZCNJykpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICAgICAgfQogICAgICAgIH07CiAgICAgICAgdmFyIHZhbGlkYXRlUGhvbmUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgICAgICAgIGlmICh2YWx1ZSA9PT0gJycpIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5omL5py65Y+3JykpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICAgICAgfQogICAgICAgIH07CgogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIC8vIOaWh+Wtl+WktOWDjwogICAgICAgICAgICBmaXJzdE5hbWU6JycsCiAgICAgICAgICAgIHJ1bGVGb3JtOiB7CiAgICAgICAgICAgICAgICB1c2VybmFtZTogJycsCiAgICAgICAgICAgICAgICBwaG9uZTogJycsCiAgICAgICAgICAgICAgICBkZXB0SWQ6IG51bGwsCiAgICAgICAgICAgICAgICBjbGFzc0lkOiBudWxsLAogICAgICAgICAgICAgICAgYWRkcmVzczogJycKICAgICAgICAgICAgfSwKICAgICAgICAgICAgLy8g57qn6IGU6YCJ5oup5Zmo55qE5YC85ZKM6YCJ6aG5CiAgICAgICAgICAgIGNhdGVnb3J5VmFsdWU6IFtdLAogICAgICAgICAgICBjYXRlZ29yeU9wdGlvbnM6IFtdLAogICAgICAgICAgICAvLyDkv53nlZnljp/mnInnmoTpgInpobnvvIjlhbzlrrnmgKfogIPomZHvvIkKICAgICAgICAgICAgZGVwdE9wdGlvbnM6IFtdLAogICAgICAgICAgICBjbGFzc09wdGlvbnM6IFtdLAoKICAgICAgICAgICAgLy8g5Zyw5Z2A55u45YWzCiAgICAgICAgICAgIGFkZHJlc3NSZWdpb246IFtdLAogICAgICAgICAgICByZWdpb25PcHRpb25zOiBbXSwKICAgICAgICAgICAgcnVsZXM6IHsKICAgICAgICAgICAgICAgIHVzZXJuYW1lOiBbCiAgICAgICAgICAgICAgICAgICAge3ZhbGlkYXRvcjogdmFsaWRhdGVVc2VybmFtZSwgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIHBob25lOiBbCiAgICAgICAgICAgICAgICAgICAge3ZhbGlkYXRvcjogdmFsaWRhdGVQaG9uZSwgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LAogICAgICAgICAgICAvL+minOiJsgogICAgICAgICAgICB0aGVtZUNvbG9yIDogeydiZyc6JyNmZmYnLCdjb2xvcic6JyMwMDAnfSwKICAgICAgICAgICAgLy/mgKfliKsKICAgICAgICAgICAgc2V4OicwJywKICAgICAgICAgICAgZHJhd2VyOiBmYWxzZSwKICAgICAgICAgICAgLy/lvZPliY3ot6/nlLEKICAgICAgICAgICAgYnJlYWRMaXN0OiBbXSwKICAgICAgICAgICAgLy/lvZPliY3lsY/luZXlrr3luqYKICAgICAgICAgICAgd2luZG93V2lkdGg6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRXaWR0aCwKICAgICAgICAgICAgYWN0aXZlSW5kZXg6ICcxJywKICAgICAgICAgICAgLy/mjqfliLboj5zljZXmmK/lkKblsZXlvIAKICAgICAgICAgICAgaXNDb2xsYXBzZTogZmFsc2UsCiAgICAgICAgICAgIGFkbWluOiAiIiwKICAgICAgICAgICAgLy8gcm9sZTogW10sCiAgICAgICAgICAgIHJvbGU6W10sCiAgICAgICAgICAgIC8v57qn6IGU6YCJ5oup5Zmo55qE5YC8CiAgICAgICAgICAgIHZhbHVlOiAiIiwKICAgICAgICAgICAgZGlhbG9nVmlzaWJsZTpmYWxzZSwKICAgICAgICAgICAgLy8g5pyq6K+76YCa55+l5pWw6YePCiAgICAgICAgICAgIHVucmVhZENvdW50OiAwCiAgICAgICAgfQogICAgfSwKICAgIHdhdGNoOiB7CiAgICAgICAgJyRyb3V0ZScodG8sIGZvcm0pIHsKICAgICAgICAgICAgdGhpcy5nZXRCcmVhZGNydW1iKCkKICAgICAgICB9CiAgICB9LAogICAgY3JlYXRlZCgpIHsKICAgICAgICAvLyDliJ3lp4vljJblnLDlnYDmlbDmja4KICAgICAgICB0aGlzLnJlZ2lvbk9wdGlvbnMgPSByZWdpb25EYXRhOwoKICAgICAgICBsZXQgdGhlbWUgPSBKU09OLnBhcnNlKHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInRoZW1lQ29sb3IiKSk7CiAgICAgICAgaWYgKHRoZW1lKXsKICAgICAgICAgICAgdGhpcy50aGVtZUNvbG9yID0geydiZyc6dGhlbWUudmFsdWUsJ2NvbG9yJzp0aGVtZS5jb2xvcn0KICAgICAgICB9CgogICAgICAgIGlmIChzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCd1c2VyJykpewogICAgICAgICAgICB0aGlzLmdldEJyZWFkY3J1bWIoKTsKICAgICAgICAgICAgY29uc3QgdXNlckRhdGEgPSBKU09OLnBhcnNlKHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInVzZXIiKSk7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIGRhdGEgZnJvbSBzZXNzaW9uOicsIHVzZXJEYXRhKTsKICAgICAgICAgICAgdGhpcy5zZXRVc2VyKHVzZXJEYXRhKTsKCiAgICAgICAgICAgIC8vIOWmguaenOaYr+e7tOS/ruWRmOaIlueuoeeQhuWRmO+8jOiOt+WPluacquivu+mAmuefpeaVsOmHjwogICAgICAgICAgICBpZiAodGhpcy51c2VyLnJvbGUgJiYgKHRoaXMudXNlci5yb2xlLmlkID09PSAxMyB8fCB0aGlzLnVzZXIucm9sZS5pZCA9PT0gMTQpKSB7CiAgICAgICAgICAgICAgICB0aGlzLmdldFVucmVhZE5vdGlmaWNhdGlvbkNvdW50KCk7CgogICAgICAgICAgICAgICAgLy8g5q+P5YiG6ZKf6I635Y+W5LiA5qyh5pyq6K+76YCa55+l5pWw6YePCiAgICAgICAgICAgICAgICBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRVbnJlYWROb3RpZmljYXRpb25Db3VudCgpOwogICAgICAgICAgICAgICAgfSwgNjAwMDApOwogICAgICAgICAgICB9CiAgICAgICAgICAgIC8vCiAgICAgICAgICAgIC8vIOajgOafpeeUqOaIt+aYr+WQpuaYr+mmluasoeeZu+W9le+8iOmAmui/h+ajgOafpXN0YXRl5a2X5q6177yJCiAgICAgICAgICAgIC8vIHN0YXRl5Li6MOihqOekuuacquWujOWWhOS/oeaBr++8jHN0YXRl5Li6MeihqOekuuW3suWujOWWhOS/oeaBrwogICAgICAgICAgICBpZiAodGhpcy51c2VyLnN0YXRlID09PSAwKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRnZXQoIi9yb2xlLyIgKyB0aGlzLnVzZXIucm9sZS5pZCkKICAgICAgICAgICAgICAgIC50aGVuKChyZXMpID0+IHsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuZGF0YSkKICAgICAgICAgICAgICAgICAgICB0aGlzLnJvbGUgPSByZXMuZGF0YS5yb2xlOwogICAgICAgICAgICAgICAgICAgIHRoaXMuZHJhd2VyID0gdHJ1ZQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi6aaW5qyh55m75b2V77yM6K+35a6M5ZaE5L+h5oGvIiwgIndhcm5pbmciKQogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfQogICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgdGhpcy4kbXNnKCLmgqjlkJHmnKrnmbvpmYYs5rKh5pyJ5p2D6ZmQIiwiZXJyb3IiKQogICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiLyIpCiAgICAgICAgfQoKICAgIH0sCiAgICBtb3VudGVkKCkgewogICAgICAgIC8vIOiOt+WPluW9k+WJjeWxj+W5leWuveW6pgogICAgICAgIHdpbmRvdy5vbnJlc2l6ZSA9ICgpID0+IHsKICAgICAgICAgICAgdGhpcy53aW5kb3dXaWR0aCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRXaWR0aAogICAgICAgIH0KICAgICAgICAvLyDmloflrZfpoK3lg48KICAgICAgICB0aGlzLnRleHRBdmF0YXIodGhpcy51c2VyLnVzZXJuYW1lKTsKICAgIH0KfQo="}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6QA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views/user", "sourcesContent": ["<template>\n    <div class=\"main\">\n        <div class=\"left\" :style=\"{width:isCollapse?'64px':'200px',background:themeColor.bg,color:themeColor.color}\" style=\"transition: .3s;\">\n            <div class=\"logo\">\n<!--                <img src=\"@s/assets/logo.jpg\" style=\"width: 26%\">-->\n            </div>\n            <el-menu\n                    :collapse-transition=\"false\"\n                    :collapse=\"isCollapse\"\n                    :router=\"true\"\n                    :default-active=\"$route.path\"\n                    :background-color=\"themeColor.bg\"\n                    :text-color=\"themeColor.color\"\n                    :unique-opened=\"true\">\n                <el-menu-item index=\"/home/\">\n                    <i class=\"el-icon-s-home\"></i>\n                    <span>首页</span>\n                </el-menu-item>\n\n                <el-submenu index=\"1\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>任务管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>发布任务</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"2\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-paperclip\"></i>\n                        <span>订单管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已发布任务</span>\n                    </el-menu-item>\n\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"3\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>公告管理</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                        <span>查看公告</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"9\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>评价管理</span>\n                    </template>\n                     <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我发布的评价</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                    <span>查看评价</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <!-- 论坛入口，只对维修员和管理员可见，用户角色ID是14 -->\n                <el-submenu index=\"10\" v-if=\"user.role.id !== 14\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-chat-dot-square\"></i>\n                        <span>维修员论坛</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>/posts\">\n                        <i class=\"el-icon-document\"></i>\n                        <span>帖子列表</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/my-posts\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我的帖子</span>\n                    </el-menu-item>\n                    <!-- 管理员角色不是14（用户角色）也不是13（维修员角色） -->\n                    <el-menu-item v-if=\"user.role.id !== 14 && user.role.id !== 13\" index=\"/home/<USER>/audit\">\n                        <i class=\"el-icon-s-check\"></i>\n                        <span>帖子审核</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/notifications\">\n                        <i class=\"el-icon-bell\"></i>\n                        <span>消息通知</span>\n                        <el-badge v-if=\"unreadCount > 0\" :value=\"unreadCount\" class=\"notification-badge\" />\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"11\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>个人中心</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">个人信息\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-menu-item v-if=\"user.role.id === 14\">\n                    <span>当前余额: {{ user.balance }}元</span>\n                    <!-- <el-button type=\"text\" @click=\"recharge(user.studentId)\">充值余额</el-button> -->\n                </el-menu-item>\n            </el-menu>\n        </div>\n\n        <div class=\"right\"\n             :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px'}\">\n            <div class=\"top\"\n                 :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px',background:themeColor.bg}\">\n                <div class=\"icon\" @click=\"isCollapse = !isCollapse\">\n                    <i :class=\"isCollapse?'el-icon-s-unfold':'el-icon-s-fold'\" :style=\"{color:themeColor.color}\"></i>\n                </div>\n                <el-menu\n\n                        :unique-opened=\"true\"\n                        :default-active=\"activeIndex\"\n                        class=\"el-menu-demo\"\n                        mode=\"horizontal\"\n                        :background-color=\"themeColor.bg\"\n                        :text-color=\"themeColor.color\"\n                        :active-text-color=\"themeColor.color\"\n                        menu-trigger=\"click\">\n\n                    <el-menu-item v-if=\"user.role.id === 14\" @click=\"recharge(user.studentId)\">充值余额</el-menu-item>\n<!--                    <el-menu-item @click=\"recharge(user.studentId)\">充值余额</el-menu-item>-->\n\n                    <el-submenu index=\"1\">\n                        <template slot=\"title\">更换主题</template>\n                        <el-menu-item v-for=\"item in theme\" @click=\"changeColor(item)\">\n                            {{item.name}}\n                        </el-menu-item>\n                    </el-submenu>\n                    <el-submenu index=\"2\">\n<!--                        <template slot=\"title\">{{user.username}}</template>-->\n                        <el-avatar slot=\"title\" style=\"background: #65c4a6; user-select: none;\">{{firstName}}</el-avatar>\n                        <el-menu-item index=\"2-1\" @click=\"exit\">退出</el-menu-item>\n                        <el-menu-item index=\"2-2\" @click=\"updPassword(user.id)\">修改密码</el-menu-item>\n                        <el-menu-item index=\"2-3\" @click=\"personalInformation()\">修改个人信息</el-menu-item>\n                    </el-submenu>\n                </el-menu>\n\n            </div>\n            <div class=\"bottom\">\n                <div class=\"bottom_top\">\n                    <el-breadcrumb separator-class=\"el-icon-arrow-right\">\n                        <el-breadcrumb-item v-for=\"item in breadList\" :to=\"item.path\" v-if=\"item.meta.title\">\n                            {{item.meta.title}}\n                        </el-breadcrumb-item>\n                    </el-breadcrumb>\n                </div>\n                <transition name=\"el-fade-in\" mode=\"out-in\">\n                    <router-view @personalInformation=\"personalInformation\"></router-view>\n                </transition>\n            </div>\n        </div>\n\n        <el-drawer\n                title=\"完善信息\"\n                :visible.sync=\"drawer\"\n                direction=\"rtl\"\n                closeDrawer=\"false\"\n                :show-close=\"false\"\n                :before-close=\"handleClose\">\n            <el-form :model=\"ruleForm\" status-icon :rules=\"rules\" ref=\"ruleForm\" label-width=\"100px\"\n                     class=\"demo-ruleForm ruleform\">\n\n                <!-- 根据角色 ID 动态显示班级信息 -->\n                <el-form-item label=\"类别\" v-if=\"user.role.id !== 14\">\n                    <el-cascader\n                            v-model=\"value\"\n                            :options=\"role.depts\"\n                            :props=\"{\n                children:'classes',\n                label:'name',\n                value:'id'\n            }\"\n                    ></el-cascader>\n                </el-form-item>\n\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"工号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\"></el-input>\n                </el-form-item>\n\n                <el-form-item label=\"性别\">\n                    <el-radio-group v-model=\"sex\">\n                        <el-radio label=\"0\">男</el-radio>\n                        <el-radio label=\"1\">女</el-radio>\n                    </el-radio-group>\n                </el-form-item>\n\n                <el-form-item>\n                    <el-button type=\"primary\" @click=\"submitForm('ruleForm')\">提交</el-button>\n                </el-form-item>\n            </el-form>\n        </el-drawer>\n\n        <el-dialog title=\"修改信息\" :visible.sync=\"dialogVisible\" :close-on-click-modal=\"false\">\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model.number=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"手机号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\" oninput=\"if(value.length>11)value=value.slice(0,11)\"></el-input>\n                </el-form-item>\n\n                <!-- 维修员可以修改类别和子类别 -->\n                <template v-if=\"user.role && user.role.id === 13\">\n                    <el-form-item label=\"类别-子类别\" prop=\"categoryValue\">\n                        <el-cascader\n                            v-model=\"categoryValue\"\n                            :options=\"categoryOptions\"\n                            :props=\"{\n                                children: 'children',\n                                label: 'name',\n                                value: 'id',\n                                checkStrictly: false\n                            }\"\n                            placeholder=\"请选择类别和子类别\"\n                            clearable\n                        ></el-cascader>\n                    </el-form-item>\n\n                    <!-- 维修员地址信息 -->\n                    <el-form-item label=\"省市区\" prop=\"addressRegion\">\n                        <el-cascader\n                            v-model=\"addressRegion\"\n                            :options=\"regionOptions\"\n                            placeholder=\"请选择省/市/区\"\n                            style=\"width: 100%\"\n                        ></el-cascader>\n                    </el-form-item>\n                    <el-form-item label=\"详细地址\" prop=\"address\">\n                        <el-input\n                            v-model=\"ruleForm.address\"\n                            type=\"textarea\"\n                            placeholder=\"请输入详细地址信息，如街道、门牌号等\"\n                            :rows=\"3\"\n                        ></el-input>\n                    </el-form-item>\n                </template>\n\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"dialogVisible = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"submitChanges\">确 定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\n    import {mapState, mapMutations} from \"vuex\"\n    import user from \"@s/store/module/user\";\n    import regionData from '@/assets/data/region.js';\n\n    export default {\n        name: \"Home\",\n        methods: {\n            ...mapMutations('user', ['setUser']),\n            changeColor(val){\n                sessionStorage.setItem(\"themeColor\",JSON.stringify(val))\n                this.themeColor = {'bg':val.value,'color':val.color};\n            },\n            //面包屑\n            getBreadcrumb() {\n                let matched = this.$route.matched;\n                if (matched[0].name != 'home') {\n                    matched = [{path: \"/home/\", meta: {title: '首页'}}].concat(matched)\n                }\n                this.breadList = matched;\n            },\n            //关闭抽屉触发的事件\n            handleClose(done) {\n                // 如果是首次登录且未完善信息，不允许关闭抽屉\n                if (this.user.state === 0) {\n                    this.$msg(\"首次登录必须完善信息\", \"error\")\n                } else {\n                    // 如果不是首次登录或已完善信息，允许关闭抽屉\n                    done()\n                }\n            },\n            submitForm(formName) {\n                this.$refs[formName].validate((valid) => {\n                    if (valid) {\n                        // 根据角色ID决定是否需要选择类别\n                        if ((this.user.role.id !== 14 && this.value) || this.user.role.id === 14) {\n                            const userData = {\n                                id: this.user.id,\n                                deptId: this.user.role.id !== 14 ? this.value[0] : null,\n                                // classId 字段在数据库中不存在，移除该字段\n                                username: this.ruleForm.username,\n                                phone: this.ruleForm.phone,\n                                sex: this.sex,\n                                // 设置状态为已完善信息\n                                state: 1\n                            }\n\n                            this.$put(\"/user\", userData)\n                                .then(res => {\n                                    if (res.data.status) {\n                                        this.drawer = false;\n                                        this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                                        // 更新本地用户信息\n                                        if (res.data.user) {\n                                            sessionStorage.setItem('user', JSON.stringify(res.data.user))\n                                            this.setUser(res.data.user)\n                                        }\n                                        this.newList(this.user.id)\n                                    } else {\n                                        this.$msg(res.data.msg, \"error\")\n                                    }\n                                })\n                                .catch(err => {\n                                    console.error('Update failed:', err)\n                                    this.$msg(err.response?.data?.msg || \"更新失败，请稍后重试\", \"error\")\n                                })\n                        } else {\n                            this.$notifyMsg(\"错误\", \"请选择类别\", \"error\")\n                        }\n                    } else {\n                        return false;\n                    }\n                });\n            },\n            //修改密码\n            updPassword(id) {\n                this.$prompt('请输入密码', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputType: 'password',\n                    closeOnClickModal:false,\n                    inputPattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,\n                    inputErrorMessage: '格式不对，密码只能输入6-16位英文和数字'\n                }).then((res) => {\n                    // console.log(res);\n                    this.$put('/user', {id: id, password: res.value})\n                    .then((res) => {\n                        this.$notifyMsg('成功', res.data.msg, 'success')\n                    })\n                }).catch(() => {\n                })\n            },\n            recharge(studentId) {\n                this.$prompt('请输入充值金额', '充值', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputPattern: /^(0\\.\\d{1,2}|[1-9]\\d*(\\.\\d{1,2})?)$/,\n                    inputErrorMessage: '请输入有效金额（最多两位小数）'\n                }).then(({ value }) => {\n                    const amount = parseFloat(value);\n\n                    // 验证金额范围\n                    if (amount < 0.01 || amount > 10000) {\n                        this.$msg(\"充值金额必须在0.01-10000元之间\", \"error\");\n                        return;\n                    }\n\n                    // 创建支付宝支付订单\n                    const payData = {\n                        subject: '校园帮账户充值',\n                        totalAmount: amount,\n                        userId: this.user.id\n                    };\n\n                    // 调用后端创建支付订单接口\n                    this.$post('api/alipay/create', payData)\n                        .then(res => {\n                            if (res.data.status) {\n                                // 在新窗口中打开支付页面\n                                const payWindow = window.open('', '_blank', 'width=800,height=600');\n                                payWindow.document.write(res.data.payForm);\n                                payWindow.document.close();\n\n                                // 监听支付窗口关闭，刷新用户信息\n                                const checkClosed = setInterval(() => {\n                                    if (payWindow.closed) {\n                                        clearInterval(checkClosed);\n                                        // 刷新用户信息\n                                        this.newList(this.user.id);\n                                        this.$msg(\"请检查支付结果\", \"info\");\n                                    }\n                                }, 1000);\n                            } else {\n                                this.$msg(res.data.msg || \"创建支付订单失败\", \"error\");\n                            }\n                        })\n                        .catch(err => {\n                            console.error('创建支付订单失败:', err);\n                            this.$msg(\"创建支付订单失败，请稍后重试\", \"error\");\n                        });\n                }).catch(() => {\n                    this.$msg(\"已取消充值\", \"info\");\n                });\n            },\n            personalInformation() {\n                this.dialogVisible = true;\n                this.ruleForm.username = this.user.username;\n                this.ruleForm.phone = this.user.phone;\n\n                // 如果是维修员，加载类别和子类别数据\n                if (this.user.role && this.user.role.id === 13) {\n                    // 设置当前的类别和子类别\n                    if (this.user.dept) {\n                        this.ruleForm.deptId = this.user.dept.id;\n                    }\n                    if (this.user.type) {\n                        this.ruleForm.classId = this.user.type.id;\n                    }\n\n                    // 设置级联选择器的初始值\n                    if (this.user.dept && this.user.type) {\n                        this.categoryValue = [this.user.dept.id, this.user.type.id];\n                    } else if (this.user.dept) {\n                        this.categoryValue = [this.user.dept.id];\n                    } else {\n                        this.categoryValue = [];\n                    }\n\n                    // 设置地址选择器的初始值\n                    if (this.user.province && this.user.city && this.user.district) {\n                        this.addressRegion = [this.user.province, this.user.city, this.user.district];\n                        this.ruleForm.address = this.user.address || '';\n                    } else {\n                        this.addressRegion = [];\n                        this.ruleForm.address = '';\n                    }\n\n                    // 加载所有可用的类别和子类别\n                    this.loadCategoryOptions();\n                }\n            },\n\n            // 加载类别和子类别的级联选项\n            loadCategoryOptions() {\n                // 先获取所有类别\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        // 保存原始类别列表（兼容性考虑）\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n\n                        // 为每个类别加载子类别\n                        const promises = res.data.dept.map(dept => {\n                            return this.$get(\"/class/list\", { deptId: dept.id })\n                            .then(classRes => {\n                                if (classRes.data.status && classRes.data.class) {\n                                    // 返回带有子类别的类别对象\n                                    return {\n                                        ...dept,\n                                        children: classRes.data.class\n                                    };\n                                }\n                                // 如果没有子类别，返回原始类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            })\n                            .catch(() => {\n                                // 出错时返回没有子类别的类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            });\n                        });\n\n                        // 等待所有子类别加载完成\n                        Promise.all(promises)\n                        .then(categories => {\n                            this.categoryOptions = categories;\n                            console.log('级联选择器选项:', this.categoryOptions);\n                        });\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadDepartments() {\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadSubCategories(deptId) {\n                if (!deptId) return;\n\n                this.$get(\"/class/list\", { deptId: deptId })\n                .then(res => {\n                    if (res.data.status && res.data.class) {\n                        this.classOptions = res.data.class;\n                        console.log('可用的子类别列表:', this.classOptions);\n\n                        // 如果当前选择的子类别不在新的子类别列表中，清空选择\n                        const exists = this.classOptions.some(item => item.id === this.ruleForm.classId);\n                        if (!exists) {\n                            this.ruleForm.classId = null;\n                        }\n                    } else {\n                        this.classOptions = [];\n                        this.ruleForm.classId = null;\n                        this.$msg(\"获取子类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取子类别列表失败:', err);\n                    this.classOptions = [];\n                    this.ruleForm.classId = null;\n                    this.$msg(\"获取子类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n            // submitChanges(){\n            //     this.$put(\"/user\",{id:this.user.id,username:this.ruleForm.username,phone:this.ruleForm.phone})\n            //     .then(res=>{\n            //         this.$notifyMsg(\"成功\",res.data.msg,\"success\",1000);\n            //         this.dialogVisible = false;\n            //         this.newList(this.user.id)\n            //     })\n            // },\n            submitChanges() {\n                // 准备要更新的用户数据\n                const userData = {\n                    id: this.user.id,\n                    username: this.ruleForm.username,\n                    phone: this.ruleForm.phone,\n                    state: 1\n                };\n\n                // 如果是维修员，添加类别和子类别信息\n                if (this.user.role && this.user.role.id === 13) {\n                    // 从级联选择器中获取类别和子类别ID\n                    if (this.categoryValue && this.categoryValue.length > 0) {\n                        // 第一个值是类别ID\n                        userData.deptId = this.categoryValue[0];\n\n                        // 如果选择了子类别（数组长度大于1），第二个值是子类别ID\n                        if (this.categoryValue.length > 1) {\n                            userData.classId = this.categoryValue[1];\n                        }\n                    }\n                    // 兼容旧版本的选择方式\n                    else {\n                        // 只有当选择了类别时才更新类别ID\n                        if (this.ruleForm.deptId) {\n                            userData.deptId = this.ruleForm.deptId;\n                        }\n\n                        // 只有当选择了子类别时才更新子类别ID\n                        if (this.ruleForm.classId) {\n                            userData.classId = this.ruleForm.classId;\n                        }\n                    }\n\n                    // 处理地址信息\n                    if (this.addressRegion && this.addressRegion.length === 3) {\n                        userData.province = this.addressRegion[0];\n                        userData.city = this.addressRegion[1];\n                        userData.district = this.addressRegion[2];\n                    }\n\n                    // 如果有详细地址，添加到更新数据中\n                    if (this.ruleForm.address) {\n                        userData.address = this.ruleForm.address;\n                    }\n                }\n\n                console.log('提交的用户数据:', userData);\n\n                // 发送更新请求\n                this.$put(\"/user\", userData)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\");\n                            this.dialogVisible = false;\n                            this.newList(this.user.id);\n                        } else {\n                            this.$msg(res.data.msg || \"更新失败\", \"error\");\n                        }\n                    })\n                    .catch(err => {\n                        console.error('更新用户信息失败:', err);\n                        this.$msg(\"更新失败，请稍后重试\", \"error\");\n                    });\n            },\n            //根据当前用户查询id\n            newList(id) {\n                this.$get(\"/user/\" + id)\n                .then((rs) => {\n                    // 确保用户状态正确更新\n                    if (rs.data.user) {\n                        console.log('User data from API:', JSON.stringify(rs.data.user, null, 2));\n                        console.log('User role ID:', rs.data.user.role ? rs.data.user.role.id : 'No role');\n                        console.log('User dept:', rs.data.user.dept ? JSON.stringify(rs.data.user.dept, null, 2) : 'No dept data');\n                        console.log('User type:', rs.data.user.type ? JSON.stringify(rs.data.user.type, null, 2) : 'No type data');\n\n                        // 如果是维修员但没有type信息，尝试重新获取用户数据\n                        if (rs.data.user.role && rs.data.user.role.id === 13 && !rs.data.user.type) {\n                            console.log('维修员没有type信息，尝试修复...');\n                            // 尝试手动设置type信息\n                            if (rs.data.user.classId) {\n                                console.log('找到classId:', rs.data.user.classId);\n                                // 获取type信息\n                                this.$get(\"/class/\" + rs.data.user.classId)\n                                .then(typeRes => {\n                                    if (typeRes.data.class) {\n                                        console.log('获取到type信息:', JSON.stringify(typeRes.data.class, null, 2));\n                                        // 手动设置type信息\n                                        rs.data.user.type = typeRes.data.class;\n                                        // 更新用户信息\n                                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user));\n                                        this.setUser(rs.data.user);\n                                    }\n                                });\n                            }\n                        }\n\n                        // 如果用户已完善信息，确保状态为1\n                        if (rs.data.user.dept && rs.data.user.state === 0) {\n                            rs.data.user.state = 1;\n                            // 更新用户状态\n                            this.$put(\"/user\", {\n                                id: rs.data.user.id,\n                                state: 1\n                            });\n                        }\n                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user))\n                        this.setUser(JSON.parse(sessionStorage.getItem(\"user\")))\n                        // 修改完名字, 清空當前firstName; 避免出現疊加\n                        this.firstName = '';\n                        this.textAvatar(rs.data.user.username);\n                    }\n                })\n            },\n            exit(){\n                sessionStorage.removeItem('user');\n                this.$router.push('/')\n            },\n            // 文字頭像\n            textAvatar(username) {\n                let arr = username.split(' ');\n                for (var i in arr) {\n                    this.firstName += arr[i].substr(0,1);\n                }\n                this.firstName = this.firstName.toLocaleUpperCase();\n                console.log('firstName->' + this.firstName);\n            },\n\n            // 获取未读通知数量\n            getUnreadNotificationCount() {\n                if (this.user && this.user.id) {\n                    this.$get('/forum/notification/unread/count', { userId: this.user.id })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.unreadCount = res.data.count;\n                        }\n                    })\n                    .catch(err => {\n                        console.error('获取未读通知数量失败:', err);\n                    });\n                }\n            }\n        },\n        computed: {\n            ...mapState('user', ['user']),\n            theme(){\n                return this.$store.state.theme.theme\n            },\n        },\n        data() {\n            var validateUsername = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入姓名'));\n                } else {\n                    callback();\n                }\n            };\n            var validatePhone = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入手机号'));\n                } else {\n                    callback();\n                }\n            };\n\n            return {\n                // 文字头像\n                firstName:'',\n                ruleForm: {\n                    username: '',\n                    phone: '',\n                    deptId: null,\n                    classId: null,\n                    address: ''\n                },\n                // 级联选择器的值和选项\n                categoryValue: [],\n                categoryOptions: [],\n                // 保留原有的选项（兼容性考虑）\n                deptOptions: [],\n                classOptions: [],\n\n                // 地址相关\n                addressRegion: [],\n                regionOptions: [],\n                rules: {\n                    username: [\n                        {validator: validateUsername, trigger: 'blur'}\n                    ],\n                    phone: [\n                        {validator: validatePhone, trigger: 'blur'}\n                    ]\n                },\n                //颜色\n                themeColor : {'bg':'#fff','color':'#000'},\n                //性别\n                sex:'0',\n                drawer: false,\n                //当前路由\n                breadList: [],\n                //当前屏幕宽度\n                windowWidth: document.documentElement.clientWidth,\n                activeIndex: '1',\n                //控制菜单是否展开\n                isCollapse: false,\n                admin: \"\",\n                // role: [],\n                role:[],\n                //级联选择器的值\n                value: \"\",\n                dialogVisible:false,\n                // 未读通知数量\n                unreadCount: 0\n            }\n        },\n        watch: {\n            '$route'(to, form) {\n                this.getBreadcrumb()\n            }\n        },\n        created() {\n            // 初始化地址数据\n            this.regionOptions = regionData;\n\n            let theme = JSON.parse(sessionStorage.getItem(\"themeColor\"));\n            if (theme){\n                this.themeColor = {'bg':theme.value,'color':theme.color}\n            }\n\n            if (sessionStorage.getItem('user')){\n                this.getBreadcrumb();\n                const userData = JSON.parse(sessionStorage.getItem(\"user\"));\n                console.log('User data from session:', userData);\n                this.setUser(userData);\n\n                // 如果是维修员或管理员，获取未读通知数量\n                if (this.user.role && (this.user.role.id === 13 || this.user.role.id === 14)) {\n                    this.getUnreadNotificationCount();\n\n                    // 每分钟获取一次未读通知数量\n                    setInterval(() => {\n                        this.getUnreadNotificationCount();\n                    }, 60000);\n                }\n                //\n                // 检查用户是否是首次登录（通过检查state字段）\n                // state为0表示未完善信息，state为1表示已完善信息\n                if (this.user.state === 0) {\n                    this.$get(\"/role/\" + this.user.role.id)\n                    .then((res) => {\n                        console.log(res.data)\n                        this.role = res.data.role;\n                        this.drawer = true\n                        this.$msg(\"首次登录，请完善信息\", \"warning\")\n                    })\n                }\n            }else {\n                this.$msg(\"您向未登陆,没有权限\",\"error\")\n                this.$router.push(\"/\")\n            }\n\n        },\n        mounted() {\n            // 获取当前屏幕宽度\n            window.onresize = () => {\n                this.windowWidth = document.documentElement.clientWidth\n            }\n            // 文字頭像\n            this.textAvatar(this.user.username);\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .main {\n        display: flex;\n        height: 90%;\n\n        .left {\n            position: fixed;\n            height: 100%;\n\n            .logo {\n                width: 90%;\n                /*color: white;*/\n                font-size: 16px;\n                text-align: center;\n                padding: 8px 0;\n                /*border: 1px solid white;*/\n                margin: 9.1px auto 0 auto;\n            }\n        }\n\n        .right {\n            transition: all 0.3s ease 0s;\n            position: relative;\n\n            .top {\n                transition: all 0.3s ease 0s;\n                position: fixed;\n                /*color: #fff;*/\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                z-index: 9;\n\n                .icon {\n                    font-size: 20px;\n                    cursor: pointer;\n                    margin-left: 10px;\n                }\n            }\n\n            .bottom {\n                width: 100%;\n                height: 100%;\n                /*background: #fff;*/\n                margin-top: 65px;\n                .bottom_top {\n                    padding: 20px;\n                }\n            }\n        }\n\n        .ruleform /deep/ .el-input {\n            width: 80% !important;\n        }\n\n        /deep/ .el-cascader {\n            width: 100% !important;\n        }\n    }\n</style>\n"]}]}