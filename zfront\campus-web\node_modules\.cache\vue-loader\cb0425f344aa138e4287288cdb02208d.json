{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue", "mtime": 1748677390692}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7bWFwU3RhdGUsIG1hcE11dGF0aW9uc30gZnJvbSAidnVleCIKaW1wb3J0IHVzZXIgZnJvbSAiQHMvc3RvcmUvbW9kdWxlL3VzZXIiOwppbXBvcnQgcmVnaW9uRGF0YSBmcm9tICdAL2Fzc2V0cy9kYXRhL3JlZ2lvbi5qcyc7CgpleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAiSG9tZSIsCiAgICBtZXRob2RzOiB7CiAgICAgICAgLi4ubWFwTXV0YXRpb25zKCd1c2VyJywgWydzZXRVc2VyJ10pLAogICAgICAgIGNoYW5nZUNvbG9yKHZhbCl7CiAgICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oInRoZW1lQ29sb3IiLEpTT04uc3RyaW5naWZ5KHZhbCkpCiAgICAgICAgICAgIHRoaXMudGhlbWVDb2xvciA9IHsnYmcnOnZhbC52YWx1ZSwnY29sb3InOnZhbC5jb2xvcn07CiAgICAgICAgfSwKICAgICAgICAvL+mdouWMheWxkQogICAgICAgIGdldEJyZWFkY3J1bWIoKSB7CiAgICAgICAgICAgIGxldCBtYXRjaGVkID0gdGhpcy4kcm91dGUubWF0Y2hlZDsKICAgICAgICAgICAgaWYgKG1hdGNoZWRbMF0ubmFtZSAhPSAnaG9tZScpIHsKICAgICAgICAgICAgICAgIG1hdGNoZWQgPSBbe3BhdGg6ICIvaG9tZS8iLCBtZXRhOiB7dGl0bGU6ICfpppbpobUnfX1dLmNvbmNhdChtYXRjaGVkKQogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuYnJlYWRMaXN0ID0gbWF0Y2hlZDsKICAgICAgICB9LAogICAgICAgIC8v5YWz6Zet5oq95bGJ6Kem5Y+R55qE5LqL5Lu2CiAgICAgICAgaGFuZGxlQ2xvc2UoZG9uZSkgewogICAgICAgICAgICAvLyDlpoLmnpzmmK/pppbmrKHnmbvlvZXkuJTmnKrlrozlloTkv6Hmga/vvIzkuI3lhYHorrjlhbPpl63mir3lsYkKICAgICAgICAgICAgaWYgKHRoaXMudXNlci5zdGF0ZSA9PT0gMCkgewogICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLpppbmrKHnmbvlvZXlv4XpobvlrozlloTkv6Hmga8iLCAiZXJyb3IiKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgLy8g5aaC5p6c5LiN5piv6aaW5qyh55m75b2V5oiW5bey5a6M5ZaE5L+h5oGv77yM5YWB6K645YWz6Zet5oq95bGJCiAgICAgICAgICAgICAgICBkb25lKCkKICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc3VibWl0Rm9ybShmb3JtTmFtZSkgewogICAgICAgICAgICB0aGlzLiRyZWZzW2Zvcm1OYW1lXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICAgICAgICAgIC8vIOagueaNruinkuiJsklE5Yaz5a6a5piv5ZCm6ZyA6KaB6YCJ5oup57G75YirCiAgICAgICAgICAgICAgICAgICAgaWYgKCh0aGlzLnVzZXIucm9sZS5pZCAhPT0gMTQgJiYgdGhpcy52YWx1ZSkgfHwgdGhpcy51c2VyLnJvbGUuaWQgPT09IDE0KSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHRoaXMudXNlci5pZCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlcHRJZDogdGhpcy51c2VyLnJvbGUuaWQgIT09IDE0ID8gdGhpcy52YWx1ZVswXSA6IG51bGwsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBjbGFzc0lkIOWtl+auteWcqOaVsOaNruW6k+S4reS4jeWtmOWcqO+8jOenu+mZpOivpeWtl+autQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlcm5hbWU6IHRoaXMucnVsZUZvcm0udXNlcm5hbWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwaG9uZTogdGhpcy5ydWxlRm9ybS5waG9uZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNleDogdGhpcy5zZXgsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDorr7nva7nirbmgIHkuLrlt7LlrozlloTkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRlOiAxCiAgICAgICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHB1dCgiL3VzZXIiLCB1c2VyRGF0YSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0YXR1cykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmRyYXdlciA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRub3RpZnlNc2coIuaIkOWKnyIsIHJlcy5kYXRhLm1zZywgInN1Y2Nlc3MiKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmm7TmlrDmnKzlnLDnlKjmiLfkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnVzZXIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ3VzZXInLCBKU09OLnN0cmluZ2lmeShyZXMuZGF0YS51c2VyKSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0VXNlcihyZXMuZGF0YS51c2VyKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCh0aGlzLnVzZXIuaWQpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKHJlcy5kYXRhLm1zZywgImVycm9yIikKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignVXBkYXRlIGZhaWxlZDonLCBlcnIpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKGVyci5yZXNwb25zZT8uZGF0YT8ubXNnIHx8ICLmm7TmlrDlpLHotKXvvIzor7fnqI3lkI7ph43or5UiLCAiZXJyb3IiKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRub3RpZnlNc2coIumUmeivryIsICLor7fpgInmi6nnsbvliKsiLCAiZXJyb3IiKQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIC8v5L+u5pS55a+G56CBCiAgICAgICAgdXBkUGFzc3dvcmQoaWQpIHsKICAgICAgICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXlr4bnoIEnLCAn5o+Q56S6JywgewogICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgICAgICBpbnB1dFR5cGU6ICdwYXNzd29yZCcsCiAgICAgICAgICAgICAgICBjbG9zZU9uQ2xpY2tNb2RhbDpmYWxzZSwKICAgICAgICAgICAgICAgIGlucHV0UGF0dGVybjogL14oPyFbMC05XSskKSg/IVthLXpBLVpdKyQpWzAtOUEtWmEtel17NiwxNn0kLywKICAgICAgICAgICAgICAgIGlucHV0RXJyb3JNZXNzYWdlOiAn5qC85byP5LiN5a+577yM5a+G56CB5Y+q6IO96L6T5YWlNi0xNuS9jeiLseaWh+WSjOaVsOWtlycKICAgICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAvLyBjb25zb2xlLmxvZyhyZXMpOwogICAgICAgICAgICAgICAgdGhpcy4kcHV0KCcvdXNlcicsIHtpZDogaWQsIHBhc3N3b3JkOiByZXMudmFsdWV9KQogICAgICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG5vdGlmeU1zZygn5oiQ5YqfJywgcmVzLmRhdGEubXNnLCAnc3VjY2VzcycpCiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIH0pCiAgICAgICAgfSwKICAgICAgICByZWNoYXJnZShzdHVkZW50SWQpIHsKICAgICAgICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXlhYXlgLzph5Hpop0nLCAn5YWF5YC8JywgewogICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgICAgICBpbnB1dFBhdHRlcm46IC9eKDBcLlxkezEsMn18WzEtOV1cZCooXC5cZHsxLDJ9KT8pJC8sCiAgICAgICAgICAgICAgICBpbnB1dEVycm9yTWVzc2FnZTogJ+ivt+i+k+WFpeacieaViOmHkemine+8iOacgOWkmuS4pOS9jeWwj+aVsO+8iScKICAgICAgICAgICAgfSkudGhlbigoeyB2YWx1ZSB9KSA9PiB7CiAgICAgICAgICAgICAgICBjb25zdCBhbW91bnQgPSBwYXJzZUZsb2F0KHZhbHVlKTsKCiAgICAgICAgICAgICAgICAvLyDpqozor4Hph5Hpop3ojIPlm7QKICAgICAgICAgICAgICAgIGlmIChhbW91bnQgPCAwLjAxIHx8IGFtb3VudCA+IDEwMDAwKSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLlhYXlgLzph5Hpop3lv4XpobvlnKgwLjAxLTEwMDAw5YWD5LmL6Ze0IiwgImVycm9yIik7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIC8vIOWIm+W7uuaUr+S7mOWuneaUr+S7mOiuouWNlQogICAgICAgICAgICAgICAgY29uc3QgcGF5RGF0YSA9IHsKICAgICAgICAgICAgICAgICAgICBzdWJqZWN0OiAn5qCh5Zut5biu6LSm5oi35YWF5YC8JywKICAgICAgICAgICAgICAgICAgICB0b3RhbEFtb3VudDogYW1vdW50LAogICAgICAgICAgICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VyLmlkCiAgICAgICAgICAgICAgICB9OwoKICAgICAgICAgICAgICAgIC8vIOiwg+eUqOWQjuerr+WIm+W7uuaUr+S7mOiuouWNleaOpeWPowogICAgICAgICAgICAgICAgdGhpcy4kcG9zdCgnYXBpL2FsaXBheS9jcmVhdGUnLCBwYXlEYXRhKQogICAgICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWcqOaWsOeql+WPo+S4reaJk+W8gOaUr+S7mOmhtemdogogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcGF5V2luZG93ID0gd2luZG93Lm9wZW4oJycsICdfYmxhbmsnLCAnd2lkdGg9ODAwLGhlaWdodD02MDAnKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBheVdpbmRvdy5kb2N1bWVudC53cml0ZShyZXMuZGF0YS5wYXlGb3JtKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBheVdpbmRvdy5kb2N1bWVudC5jbG9zZSgpOwoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWumuS5iei9ruivouasoeaVsOWSjOmXtOmalAogICAgICAgICAgICAgICAgICAgICAgICAgICAgbGV0IHBvbGxDb3VudCA9IDA7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtYXhQb2xscyA9IDYwOyAvLyDmnIDlpJrova7or6I2MOasoQogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcG9sbEludGVydmFsID0gMzAwMDsgLy8g5q+PM+enkui9ruivouS4gOasoQoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOiOt+WPluiuouWNleWPt++8iOS7juWTjeW6lOS4reiOt+WPlu+8iQogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgb3JkZXJObyA9IHJlcy5kYXRhLm9yZGVyTm87IC8vIOWBh+iuvuWQjuerr+i/lOWbnuS6hm9yZGVyTm8KCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlvIDlp4vova7or6LmlK/ku5jnirbmgIEKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNoZWNrUGF5bWVudCA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzmlK/ku5jnqpflj6PlhbPpl63miJbovr7liLDmnIDlpKfova7or6LmrKHmlbDvvIzlgZzmraLova7or6IKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocG9sbENvdW50ID49IG1heFBvbGxzIHx8IHBheVdpbmRvdy5jbG9zZWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja1BheW1lbnQpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5pyA5ZCO5p+l6K+i5LiA5qyh5pSv5LuY54q25oCBCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJGdldChgYXBpL2FsaXBheS9xdWVyeS8ke29yZGVyTm99YCkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKHF1ZXJ5UmVzID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocXVlcnlSZXMuZGF0YS5zdGF0dXMgJiYgcXVlcnlSZXMuZGF0YS5kYXRhLnN0YXR1cyA9PT0gMSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmlK/ku5jmiJDlip8KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5uZXdMaXN0KHRoaXMudXNlci5pZCk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi5YWF5YC85oiQ5YqfIiwgInN1Y2Nlc3MiKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmlK/ku5jmnKrlrozmiJAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLmlK/ku5jmnKrlrozmiJDvvIzlpoLlt7LmlK/ku5jor7fnqI3lkI7liLfmlrDmn6XnnIsiLCAid2FybmluZyIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi5p+l6K+i5pSv5LuY54q25oCB5aSx6LSl77yM5aaC5bey5pSv5LuY6K+356iN5ZCO5Yi35paw5p+l55yLIiwgIndhcm5pbmciKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlrprmnJ/mn6Xor6LmlK/ku5jnirbmgIEKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRnZXQoYGFwaS9hbGlwYXkvcXVlcnkvJHtvcmRlck5vfWApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKHF1ZXJ5UmVzID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChxdWVyeVJlcy5kYXRhLnN0YXR1cyAmJiBxdWVyeVJlcy5kYXRhLmRhdGEuc3RhdHVzID09PSAxKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5pSv5LuY5oiQ5YqfCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja1BheW1lbnQpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCh0aGlzLnVzZXIuaWQpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi5YWF5YC85oiQ5YqfIiwgInN1Y2Nlc3MiKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXBheVdpbmRvdy5jbG9zZWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGF5V2luZG93LmNsb3NlKCk7IC8vIOiHquWKqOWFs+mXreaUr+S7mOeql+WPowogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmn6Xor6LmlK/ku5jnirbmgIHlpLHotKU6JywgZXJyKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBvbGxDb3VudCsrOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwgcG9sbEludGVydmFsKTsKCiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtc2cocmVzLmRhdGEubXNnIHx8ICLliJvlu7rmlK/ku5jorqLljZXlpLHotKUiLCAiZXJyb3IiKTsKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIm+W7uuaUr+S7mOiuouWNleWksei0pTonLCBlcnIpOwogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtc2coIuWIm+W7uuaUr+S7mOiuouWNleWksei0pe+8jOivt+eojeWQjumHjeivlSIsICJlcnJvciIpOwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgICAgICB0aGlzLiRtc2coIuW3suWPlua2iOWFheWAvCIsICJpbmZvIik7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0sCiAgICAgICAgcGVyc29uYWxJbmZvcm1hdGlvbigpIHsKICAgICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS51c2VybmFtZSA9IHRoaXMudXNlci51c2VybmFtZTsKICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5waG9uZSA9IHRoaXMudXNlci5waG9uZTsKCiAgICAgICAgICAgIC8vIOWmguaenOaYr+e7tOS/ruWRmO+8jOWKoOi9veexu+WIq+WSjOWtkOexu+WIq+aVsOaNrgogICAgICAgICAgICBpZiAodGhpcy51c2VyLnJvbGUgJiYgdGhpcy51c2VyLnJvbGUuaWQgPT09IDEzKSB7CiAgICAgICAgICAgICAgICAvLyDorr7nva7lvZPliY3nmoTnsbvliKvlkozlrZDnsbvliKsKICAgICAgICAgICAgICAgIGlmICh0aGlzLnVzZXIuZGVwdCkgewogICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uZGVwdElkID0gdGhpcy51c2VyLmRlcHQuaWQ7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAodGhpcy51c2VyLnR5cGUpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmNsYXNzSWQgPSB0aGlzLnVzZXIudHlwZS5pZDsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAvLyDorr7nva7nuqfogZTpgInmi6nlmajnmoTliJ3lp4vlgLwKICAgICAgICAgICAgICAgIGlmICh0aGlzLnVzZXIuZGVwdCAmJiB0aGlzLnVzZXIudHlwZSkgewogICAgICAgICAgICAgICAgICAgIHRoaXMuY2F0ZWdvcnlWYWx1ZSA9IFt0aGlzLnVzZXIuZGVwdC5pZCwgdGhpcy51c2VyLnR5cGUuaWRdOwogICAgICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLnVzZXIuZGVwdCkgewogICAgICAgICAgICAgICAgICAgIHRoaXMuY2F0ZWdvcnlWYWx1ZSA9IFt0aGlzLnVzZXIuZGVwdC5pZF07CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIHRoaXMuY2F0ZWdvcnlWYWx1ZSA9IFtdOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIC8vIOiuvue9ruWcsOWdgOmAieaLqeWZqOeahOWIneWni+WAvAogICAgICAgICAgICAgICAgaWYgKHRoaXMudXNlci5wcm92aW5jZSAmJiB0aGlzLnVzZXIuY2l0eSAmJiB0aGlzLnVzZXIuZGlzdHJpY3QpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmFkZHJlc3NSZWdpb24gPSBbdGhpcy51c2VyLnByb3ZpbmNlLCB0aGlzLnVzZXIuY2l0eSwgdGhpcy51c2VyLmRpc3RyaWN0XTsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmFkZHJlc3MgPSB0aGlzLnVzZXIuYWRkcmVzcyB8fCAnJzsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5hZGRyZXNzUmVnaW9uID0gW107CiAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5hZGRyZXNzID0gJyc7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgLy8g5Yqg6L295omA5pyJ5Y+v55So55qE57G75Yir5ZKM5a2Q57G75YirCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRDYXRlZ29yeU9wdGlvbnMoKTsKICAgICAgICAgICAgfQogICAgICAgIH0sCgogICAgICAgIC8vIOWKoOi9veexu+WIq+WSjOWtkOexu+WIq+eahOe6p+iBlOmAiemhuQogICAgICAgIGxvYWRDYXRlZ29yeU9wdGlvbnMoKSB7CiAgICAgICAgICAgIC8vIOWFiOiOt+WPluaJgOacieexu+WIqwogICAgICAgICAgICB0aGlzLiRnZXQoIi9kZXB0L2xpc3QiLCB7IHJvbGVJZDogMTMgfSkKICAgICAgICAgICAgLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMgJiYgcmVzLmRhdGEuZGVwdCkgewogICAgICAgICAgICAgICAgICAgIC8vIOS/neWtmOWOn+Wni+exu+WIq+WIl+ihqO+8iOWFvOWuueaAp+iAg+iZke+8iQogICAgICAgICAgICAgICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSByZXMuZGF0YS5kZXB0OwogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflj6/nlKjnmoTnsbvliKvliJfooag6JywgdGhpcy5kZXB0T3B0aW9ucyk7CgogICAgICAgICAgICAgICAgICAgIC8vIOS4uuavj+S4quexu+WIq+WKoOi9veWtkOexu+WIqwogICAgICAgICAgICAgICAgICAgIGNvbnN0IHByb21pc2VzID0gcmVzLmRhdGEuZGVwdC5tYXAoZGVwdCA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLiRnZXQoIi9jbGFzcy9saXN0IiwgeyBkZXB0SWQ6IGRlcHQuaWQgfSkKICAgICAgICAgICAgICAgICAgICAgICAgLnRoZW4oY2xhc3NSZXMgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNsYXNzUmVzLmRhdGEuc3RhdHVzICYmIGNsYXNzUmVzLmRhdGEuY2xhc3MpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDov5Tlm57luKbmnInlrZDnsbvliKvnmoTnsbvliKvlr7nosaEKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5kZXB0LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogY2xhc3NSZXMuZGF0YS5jbGFzcwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzmsqHmnInlrZDnsbvliKvvvIzov5Tlm57ljp/lp4vnsbvliKvlr7nosaEKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZGVwdCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogW10KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlh7rplJnml7bov5Tlm57msqHmnInlrZDnsbvliKvnmoTnsbvliKvlr7nosaEKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZGVwdCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogW10KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgICAgICAvLyDnrYnlvoXmiYDmnInlrZDnsbvliKvliqDovb3lrozmiJAKICAgICAgICAgICAgICAgICAgICBQcm9taXNlLmFsbChwcm9taXNlcykKICAgICAgICAgICAgICAgICAgICAudGhlbihjYXRlZ29yaWVzID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jYXRlZ29yeU9wdGlvbnMgPSBjYXRlZ29yaWVzOwogICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn57qn6IGU6YCJ5oup5Zmo6YCJ6aG5OicsIHRoaXMuY2F0ZWdvcnlPcHRpb25zKTsKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLojrflj5bnsbvliKvliJfooajlpLHotKUiLCAiZXJyb3IiKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnsbvliKvliJfooajlpLHotKU6JywgZXJyKTsKICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi6I635Y+W57G75Yir5YiX6KGo5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIiwgImVycm9yIik7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0sCgogICAgICAgIC8vIOS/neeVmeWOn+acieeahOaWueazle+8iOWFvOWuueaAp+iAg+iZke+8iQogICAgICAgIGxvYWREZXBhcnRtZW50cygpIHsKICAgICAgICAgICAgdGhpcy4kZ2V0KCIvZGVwdC9saXN0IiwgeyByb2xlSWQ6IDEzIH0pCiAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuc3RhdHVzICYmIHJlcy5kYXRhLmRlcHQpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gcmVzLmRhdGEuZGVwdDsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5Y+v55So55qE57G75Yir5YiX6KGoOicsIHRoaXMuZGVwdE9wdGlvbnMpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtc2coIuiOt+WPluexu+WIq+WIl+ihqOWksei0pSIsICJlcnJvciIpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgICAuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluexu+WIq+WIl+ihqOWksei0pTonLCBlcnIpOwogICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLojrflj5bnsbvliKvliJfooajlpLHotKXvvIzor7fnqI3lkI7ph43or5UiLCAiZXJyb3IiKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKCiAgICAgICAgLy8g5L+d55WZ5Y6f5pyJ55qE5pa55rOV77yI5YW85a655oCn6ICD6JmR77yJCiAgICAgICAgbG9hZFN1YkNhdGVnb3JpZXMoZGVwdElkKSB7CiAgICAgICAgICAgIGlmICghZGVwdElkKSByZXR1cm47CgogICAgICAgICAgICB0aGlzLiRnZXQoIi9jbGFzcy9saXN0IiwgeyBkZXB0SWQ6IGRlcHRJZCB9KQogICAgICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0YXR1cyAmJiByZXMuZGF0YS5jbGFzcykgewogICAgICAgICAgICAgICAgICAgIHRoaXMuY2xhc3NPcHRpb25zID0gcmVzLmRhdGEuY2xhc3M7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+WPr+eUqOeahOWtkOexu+WIq+WIl+ihqDonLCB0aGlzLmNsYXNzT3B0aW9ucyk7CgogICAgICAgICAgICAgICAgICAgIC8vIOWmguaenOW9k+WJjemAieaLqeeahOWtkOexu+WIq+S4jeWcqOaWsOeahOWtkOexu+WIq+WIl+ihqOS4re+8jOa4heepuumAieaLqQogICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0cyA9IHRoaXMuY2xhc3NPcHRpb25zLnNvbWUoaXRlbSA9PiBpdGVtLmlkID09PSB0aGlzLnJ1bGVGb3JtLmNsYXNzSWQpOwogICAgICAgICAgICAgICAgICAgIGlmICghZXhpc3RzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uY2xhc3NJZCA9IG51bGw7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmNsYXNzT3B0aW9ucyA9IFtdOwogICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uY2xhc3NJZCA9IG51bGw7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLojrflj5blrZDnsbvliKvliJfooajlpLHotKUiLCAiZXJyb3IiKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blrZDnsbvliKvliJfooajlpLHotKU6JywgZXJyKTsKICAgICAgICAgICAgICAgIHRoaXMuY2xhc3NPcHRpb25zID0gW107CiAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmNsYXNzSWQgPSBudWxsOwogICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLojrflj5blrZDnsbvliKvliJfooajlpLHotKXvvIzor7fnqI3lkI7ph43or5UiLCAiZXJyb3IiKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICAvLyBzdWJtaXRDaGFuZ2VzKCl7CiAgICAgICAgLy8gICAgIHRoaXMuJHB1dCgiL3VzZXIiLHtpZDp0aGlzLnVzZXIuaWQsdXNlcm5hbWU6dGhpcy5ydWxlRm9ybS51c2VybmFtZSxwaG9uZTp0aGlzLnJ1bGVGb3JtLnBob25lfSkKICAgICAgICAvLyAgICAgLnRoZW4ocmVzPT57CiAgICAgICAgLy8gICAgICAgICB0aGlzLiRub3RpZnlNc2coIuaIkOWKnyIscmVzLmRhdGEubXNnLCJzdWNjZXNzIiwxMDAwKTsKICAgICAgICAvLyAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIC8vICAgICAgICAgdGhpcy5uZXdMaXN0KHRoaXMudXNlci5pZCkKICAgICAgICAvLyAgICAgfSkKICAgICAgICAvLyB9LAogICAgICAgIHN1Ym1pdENoYW5nZXMoKSB7CiAgICAgICAgICAgIC8vIOWHhuWkh+imgeabtOaWsOeahOeUqOaIt+aVsOaNrgogICAgICAgICAgICBjb25zdCB1c2VyRGF0YSA9IHsKICAgICAgICAgICAgICAgIGlkOiB0aGlzLnVzZXIuaWQsCiAgICAgICAgICAgICAgICB1c2VybmFtZTogdGhpcy5ydWxlRm9ybS51c2VybmFtZSwKICAgICAgICAgICAgICAgIHBob25lOiB0aGlzLnJ1bGVGb3JtLnBob25lLAogICAgICAgICAgICAgICAgc3RhdGU6IDEKICAgICAgICAgICAgfTsKCiAgICAgICAgICAgIC8vIOWmguaenOaYr+e7tOS/ruWRmO+8jOa3u+WKoOexu+WIq+WSjOWtkOexu+WIq+S/oeaBrwogICAgICAgICAgICBpZiAodGhpcy51c2VyLnJvbGUgJiYgdGhpcy51c2VyLnJvbGUuaWQgPT09IDEzKSB7CiAgICAgICAgICAgICAgICAvLyDku47nuqfogZTpgInmi6nlmajkuK3ojrflj5bnsbvliKvlkozlrZDnsbvliKtJRAogICAgICAgICAgICAgICAgaWYgKHRoaXMuY2F0ZWdvcnlWYWx1ZSAmJiB0aGlzLmNhdGVnb3J5VmFsdWUubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgIC8vIOesrOS4gOS4quWAvOaYr+exu+WIq0lECiAgICAgICAgICAgICAgICAgICAgdXNlckRhdGEuZGVwdElkID0gdGhpcy5jYXRlZ29yeVZhbHVlWzBdOwoKICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzpgInmi6nkuoblrZDnsbvliKvvvIjmlbDnu4Tplb/luqblpKfkuo4x77yJ77yM56ys5LqM5Liq5YC85piv5a2Q57G75YirSUQKICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5jYXRlZ29yeVZhbHVlLmxlbmd0aCA+IDEpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdXNlckRhdGEuY2xhc3NJZCA9IHRoaXMuY2F0ZWdvcnlWYWx1ZVsxXTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAvLyDlhbzlrrnml6fniYjmnKznmoTpgInmi6nmlrnlvI8KICAgICAgICAgICAgICAgIGVsc2UgewogICAgICAgICAgICAgICAgICAgIC8vIOWPquacieW9k+mAieaLqeS6huexu+WIq+aXtuaJjeabtOaWsOexu+WIq0lECiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMucnVsZUZvcm0uZGVwdElkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJEYXRhLmRlcHRJZCA9IHRoaXMucnVsZUZvcm0uZGVwdElkOwogICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgLy8g5Y+q5pyJ5b2T6YCJ5oup5LqG5a2Q57G75Yir5pe25omN5pu05paw5a2Q57G75YirSUQKICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5ydWxlRm9ybS5jbGFzc0lkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJEYXRhLmNsYXNzSWQgPSB0aGlzLnJ1bGVGb3JtLmNsYXNzSWQ7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIC8vIOWkhOeQhuWcsOWdgOS/oeaBrwogICAgICAgICAgICAgICAgaWYgKHRoaXMuYWRkcmVzc1JlZ2lvbiAmJiB0aGlzLmFkZHJlc3NSZWdpb24ubGVuZ3RoID09PSAzKSB7CiAgICAgICAgICAgICAgICAgICAgdXNlckRhdGEucHJvdmluY2UgPSB0aGlzLmFkZHJlc3NSZWdpb25bMF07CiAgICAgICAgICAgICAgICAgICAgdXNlckRhdGEuY2l0eSA9IHRoaXMuYWRkcmVzc1JlZ2lvblsxXTsKICAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5kaXN0cmljdCA9IHRoaXMuYWRkcmVzc1JlZ2lvblsyXTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAvLyDlpoLmnpzmnInor6bnu4blnLDlnYDvvIzmt7vliqDliLDmm7TmlrDmlbDmja7kuK0KICAgICAgICAgICAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLmFkZHJlc3MpIHsKICAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5hZGRyZXNzID0gdGhpcy5ydWxlRm9ybS5hZGRyZXNzOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CgogICAgICAgICAgICBjb25zb2xlLmxvZygn5o+Q5Lqk55qE55So5oi35pWw5o2uOicsIHVzZXJEYXRhKTsKCiAgICAgICAgICAgIC8vIOWPkemAgeabtOaWsOivt+axggogICAgICAgICAgICB0aGlzLiRwdXQoIi91c2VyIiwgdXNlckRhdGEpCiAgICAgICAgICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbm90aWZ5TXNnKCLmiJDlip8iLCByZXMuZGF0YS5tc2csICJzdWNjZXNzIik7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm5ld0xpc3QodGhpcy51c2VyLmlkKTsKICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtc2cocmVzLmRhdGEubXNnIHx8ICLmm7TmlrDlpLHotKUiLCAiZXJyb3IiKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5pu05paw55So5oi35L+h5oGv5aSx6LSlOicsIGVycik7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLmm7TmlrDlpLHotKXvvIzor7fnqI3lkI7ph43or5UiLCAiZXJyb3IiKTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgIH0sCiAgICAgICAgLy/moLnmja7lvZPliY3nlKjmiLfmn6Xor6JpZAogICAgICAgIG5ld0xpc3QoaWQpIHsKICAgICAgICAgICAgdGhpcy4kZ2V0KCIvdXNlci8iICsgaWQpCiAgICAgICAgICAgIC50aGVuKChycykgPT4gewogICAgICAgICAgICAgICAgLy8g56Gu5L+d55So5oi354q25oCB5q2j56Gu5pu05pawCiAgICAgICAgICAgICAgICBpZiAocnMuZGF0YS51c2VyKSB7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgZGF0YSBmcm9tIEFQSTonLCBKU09OLnN0cmluZ2lmeShycy5kYXRhLnVzZXIsIG51bGwsIDIpKTsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVXNlciByb2xlIElEOicsIHJzLmRhdGEudXNlci5yb2xlID8gcnMuZGF0YS51c2VyLnJvbGUuaWQgOiAnTm8gcm9sZScpOwogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIGRlcHQ6JywgcnMuZGF0YS51c2VyLmRlcHQgPyBKU09OLnN0cmluZ2lmeShycy5kYXRhLnVzZXIuZGVwdCwgbnVsbCwgMikgOiAnTm8gZGVwdCBkYXRhJyk7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgdHlwZTonLCBycy5kYXRhLnVzZXIudHlwZSA/IEpTT04uc3RyaW5naWZ5KHJzLmRhdGEudXNlci50eXBlLCBudWxsLCAyKSA6ICdObyB0eXBlIGRhdGEnKTsKCiAgICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c5piv57u05L+u5ZGY5L2G5rKh5pyJdHlwZeS/oeaBr++8jOWwneivlemHjeaWsOiOt+WPlueUqOaIt+aVsOaNrgogICAgICAgICAgICAgICAgICAgIGlmIChycy5kYXRhLnVzZXIucm9sZSAmJiBycy5kYXRhLnVzZXIucm9sZS5pZCA9PT0gMTMgJiYgIXJzLmRhdGEudXNlci50eXBlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfnu7Tkv67lkZjmsqHmnIl0eXBl5L+h5oGv77yM5bCd6K+V5L+u5aSNLi4uJyk7CiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWwneivleaJi+WKqOiuvue9rnR5cGXkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJzLmRhdGEudXNlci5jbGFzc0lkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5om+5YiwY2xhc3NJZDonLCBycy5kYXRhLnVzZXIuY2xhc3NJZCk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDojrflj5Z0eXBl5L+h5oGvCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRnZXQoIi9jbGFzcy8iICsgcnMuZGF0YS51c2VyLmNsYXNzSWQpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAudGhlbih0eXBlUmVzID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodHlwZVJlcy5kYXRhLmNsYXNzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bliLB0eXBl5L+h5oGvOicsIEpTT04uc3RyaW5naWZ5KHR5cGVSZXMuZGF0YS5jbGFzcywgbnVsbCwgMikpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmiYvliqjorr7nva50eXBl5L+h5oGvCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJzLmRhdGEudXNlci50eXBlID0gdHlwZVJlcy5kYXRhLmNsYXNzOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmm7TmlrDnlKjmiLfkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSgidXNlciIsIEpTT04uc3RyaW5naWZ5KHJzLmRhdGEudXNlcikpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNldFVzZXIocnMuZGF0YS51c2VyKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c55So5oi35bey5a6M5ZaE5L+h5oGv77yM56Gu5L+d54q25oCB5Li6MQogICAgICAgICAgICAgICAgICAgIGlmIChycy5kYXRhLnVzZXIuZGVwdCAmJiBycy5kYXRhLnVzZXIuc3RhdGUgPT09IDApIHsKICAgICAgICAgICAgICAgICAgICAgICAgcnMuZGF0YS51c2VyLnN0YXRlID0gMTsKICAgICAgICAgICAgICAgICAgICAgICAgLy8g5pu05paw55So5oi354q25oCBCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHB1dCgiL3VzZXIiLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogcnMuZGF0YS51c2VyLmlkLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGU6IDEKICAgICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oInVzZXIiLCBKU09OLnN0cmluZ2lmeShycy5kYXRhLnVzZXIpKQogICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0VXNlcihKU09OLnBhcnNlKHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInVzZXIiKSkpCiAgICAgICAgICAgICAgICAgICAgLy8g5L+u5pS55a6M5ZCN5a2XLCDmuIXnqbrnlbbliY1maXJzdE5hbWU7IOmBv+WFjeWHuuePvueWiuWKoAogICAgICAgICAgICAgICAgICAgIHRoaXMuZmlyc3ROYW1lID0gJyc7CiAgICAgICAgICAgICAgICAgICAgdGhpcy50ZXh0QXZhdGFyKHJzLmRhdGEudXNlci51c2VybmFtZSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgfSwKICAgICAgICBleGl0KCl7CiAgICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXInKTsKICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy8nKQogICAgICAgIH0sCiAgICAgICAgLy8g5paH5a2X6aCt5YOPCiAgICAgICAgdGV4dEF2YXRhcih1c2VybmFtZSkgewogICAgICAgICAgICBsZXQgYXJyID0gdXNlcm5hbWUuc3BsaXQoJyAnKTsKICAgICAgICAgICAgZm9yICh2YXIgaSBpbiBhcnIpIHsKICAgICAgICAgICAgICAgIHRoaXMuZmlyc3ROYW1lICs9IGFycltpXS5zdWJzdHIoMCwxKTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmZpcnN0TmFtZSA9IHRoaXMuZmlyc3ROYW1lLnRvTG9jYWxlVXBwZXJDYXNlKCk7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdmaXJzdE5hbWUtPicgKyB0aGlzLmZpcnN0TmFtZSk7CiAgICAgICAgfSwKCiAgICAgICAgLy8g6I635Y+W5pyq6K+76YCa55+l5pWw6YePCiAgICAgICAgZ2V0VW5yZWFkTm90aWZpY2F0aW9uQ291bnQoKSB7CiAgICAgICAgICAgIGlmICh0aGlzLnVzZXIgJiYgdGhpcy51c2VyLmlkKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRnZXQoJy9mb3J1bS9ub3RpZmljYXRpb24vdW5yZWFkL2NvdW50JywgeyB1c2VySWQ6IHRoaXMudXNlci5pZCB9KQogICAgICAgICAgICAgICAgLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuc3RhdHVzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudW5yZWFkQ291bnQgPSByZXMuZGF0YS5jb3VudDsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5pyq6K+76YCa55+l5pWw6YeP5aSx6LSlOicsIGVycik7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgIH0KICAgIH0sCiAgICBjb21wdXRlZDogewogICAgICAgIC4uLm1hcFN0YXRlKCd1c2VyJywgWyd1c2VyJ10pLAogICAgICAgIHRoZW1lKCl7CiAgICAgICAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS50aGVtZS50aGVtZQogICAgICAgIH0sCiAgICB9LAogICAgZGF0YSgpIHsKICAgICAgICB2YXIgdmFsaWRhdGVVc2VybmFtZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgICAgICAgaWYgKHZhbHVlID09PSAnJykgewogICAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfor7fovpPlhaXlp5PlkI0nKSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjaygpOwogICAgICAgICAgICB9CiAgICAgICAgfTsKICAgICAgICB2YXIgdmFsaWRhdGVQaG9uZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgICAgICAgaWYgKHZhbHVlID09PSAnJykgewogICAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfor7fovpPlhaXmiYvmnLrlj7cnKSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjaygpOwogICAgICAgICAgICB9CiAgICAgICAgfTsKCiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgLy8g5paH5a2X5aS05YOPCiAgICAgICAgICAgIGZpcnN0TmFtZTonJywKICAgICAgICAgICAgcnVsZUZvcm06IHsKICAgICAgICAgICAgICAgIHVzZXJuYW1lOiAnJywKICAgICAgICAgICAgICAgIHBob25lOiAnJywKICAgICAgICAgICAgICAgIGRlcHRJZDogbnVsbCwKICAgICAgICAgICAgICAgIGNsYXNzSWQ6IG51bGwsCiAgICAgICAgICAgICAgICBhZGRyZXNzOiAnJwogICAgICAgICAgICB9LAogICAgICAgICAgICAvLyDnuqfogZTpgInmi6nlmajnmoTlgLzlkozpgInpobkKICAgICAgICAgICAgY2F0ZWdvcnlWYWx1ZTogW10sCiAgICAgICAgICAgIGNhdGVnb3J5T3B0aW9uczogW10sCiAgICAgICAgICAgIC8vIOS/neeVmeWOn+acieeahOmAiemhue+8iOWFvOWuueaAp+iAg+iZke+8iQogICAgICAgICAgICBkZXB0T3B0aW9uczogW10sCiAgICAgICAgICAgIGNsYXNzT3B0aW9uczogW10sCgogICAgICAgICAgICAvLyDlnLDlnYDnm7jlhbMKICAgICAgICAgICAgYWRkcmVzc1JlZ2lvbjogW10sCiAgICAgICAgICAgIHJlZ2lvbk9wdGlvbnM6IFtdLAogICAgICAgICAgICBydWxlczogewogICAgICAgICAgICAgICAgdXNlcm5hbWU6IFsKICAgICAgICAgICAgICAgICAgICB7dmFsaWRhdG9yOiB2YWxpZGF0ZVVzZXJuYW1lLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgcGhvbmU6IFsKICAgICAgICAgICAgICAgICAgICB7dmFsaWRhdG9yOiB2YWxpZGF0ZVBob25lLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIC8v6aKc6ImyCiAgICAgICAgICAgIHRoZW1lQ29sb3IgOiB7J2JnJzonI2ZmZicsJ2NvbG9yJzonIzAwMCd9LAogICAgICAgICAgICAvL+aAp+WIqwogICAgICAgICAgICBzZXg6JzAnLAogICAgICAgICAgICBkcmF3ZXI6IGZhbHNlLAogICAgICAgICAgICAvL+W9k+WJjei3r+eUsQogICAgICAgICAgICBicmVhZExpc3Q6IFtdLAogICAgICAgICAgICAvL+W9k+WJjeWxj+W5leWuveW6pgogICAgICAgICAgICB3aW5kb3dXaWR0aDogZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsaWVudFdpZHRoLAogICAgICAgICAgICBhY3RpdmVJbmRleDogJzEnLAogICAgICAgICAgICAvL+aOp+WItuiPnOWNleaYr+WQpuWxleW8gAogICAgICAgICAgICBpc0NvbGxhcHNlOiBmYWxzZSwKICAgICAgICAgICAgYWRtaW46ICIiLAogICAgICAgICAgICAvLyByb2xlOiBbXSwKICAgICAgICAgICAgcm9sZTpbXSwKICAgICAgICAgICAgLy/nuqfogZTpgInmi6nlmajnmoTlgLwKICAgICAgICAgICAgdmFsdWU6ICIiLAogICAgICAgICAgICBkaWFsb2dWaXNpYmxlOmZhbHNlLAogICAgICAgICAgICAvLyDmnKror7vpgJrnn6XmlbDph48KICAgICAgICAgICAgdW5yZWFkQ291bnQ6IDAKICAgICAgICB9CiAgICB9LAogICAgd2F0Y2g6IHsKICAgICAgICAnJHJvdXRlJyh0bywgZm9ybSkgewogICAgICAgICAgICB0aGlzLmdldEJyZWFkY3J1bWIoKQogICAgICAgIH0KICAgIH0sCiAgICBjcmVhdGVkKCkgewogICAgICAgIC8vIOWIneWni+WMluWcsOWdgOaVsOaNrgogICAgICAgIHRoaXMucmVnaW9uT3B0aW9ucyA9IHJlZ2lvbkRhdGE7CgogICAgICAgIGxldCB0aGVtZSA9IEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgidGhlbWVDb2xvciIpKTsKICAgICAgICBpZiAodGhlbWUpewogICAgICAgICAgICB0aGlzLnRoZW1lQ29sb3IgPSB7J2JnJzp0aGVtZS52YWx1ZSwnY29sb3InOnRoZW1lLmNvbG9yfQogICAgICAgIH0KCiAgICAgICAgaWYgKHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3VzZXInKSl7CiAgICAgICAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogICAgICAgICAgICBjb25zdCB1c2VyRGF0YSA9IEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgidXNlciIpKTsKICAgICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgZGF0YSBmcm9tIHNlc3Npb246JywgdXNlckRhdGEpOwogICAgICAgICAgICB0aGlzLnNldFVzZXIodXNlckRhdGEpOwoKICAgICAgICAgICAgLy8g5aaC5p6c5piv57u05L+u5ZGY5oiW566h55CG5ZGY77yM6I635Y+W5pyq6K+76YCa55+l5pWw6YePCiAgICAgICAgICAgIGlmICh0aGlzLnVzZXIucm9sZSAmJiAodGhpcy51c2VyLnJvbGUuaWQgPT09IDEzIHx8IHRoaXMudXNlci5yb2xlLmlkID09PSAxNCkpIHsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0VW5yZWFkTm90aWZpY2F0aW9uQ291bnQoKTsKCiAgICAgICAgICAgICAgICAvLyDmr4/liIbpkp/ojrflj5bkuIDmrKHmnKror7vpgJrnn6XmlbDph48KICAgICAgICAgICAgICAgIHNldEludGVydmFsKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmdldFVucmVhZE5vdGlmaWNhdGlvbkNvdW50KCk7CiAgICAgICAgICAgICAgICB9LCA2MDAwMCk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgLy8KICAgICAgICAgICAgLy8g5qOA5p+l55So5oi35piv5ZCm5piv6aaW5qyh55m75b2V77yI6YCa6L+H5qOA5p+lc3RhdGXlrZfmrrXvvIkKICAgICAgICAgICAgLy8gc3RhdGXkuLow6KGo56S65pyq5a6M5ZaE5L+h5oGv77yMc3RhdGXkuLox6KGo56S65bey5a6M5ZaE5L+h5oGvCiAgICAgICAgICAgIGlmICh0aGlzLnVzZXIuc3RhdGUgPT09IDApIHsKICAgICAgICAgICAgICAgIHRoaXMuJGdldCgiL3JvbGUvIiArIHRoaXMudXNlci5yb2xlLmlkKQogICAgICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5kYXRhKQogICAgICAgICAgICAgICAgICAgIHRoaXMucm9sZSA9IHJlcy5kYXRhLnJvbGU7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5kcmF3ZXIgPSB0cnVlCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLpppbmrKHnmbvlvZXvvIzor7flrozlloTkv6Hmga8iLCAid2FybmluZyIpCiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9CiAgICAgICAgfWVsc2UgewogICAgICAgICAgICB0aGlzLiRtc2coIuaCqOWQkeacqueZu+mZhizmsqHmnInmnYPpmZAiLCJlcnJvciIpCiAgICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvIikKICAgICAgICB9CgogICAgfSwKICAgIG1vdW50ZWQoKSB7CiAgICAgICAgLy8g6I635Y+W5b2T5YmN5bGP5bmV5a695bqmCiAgICAgICAgd2luZG93Lm9ucmVzaXplID0gKCkgPT4gewogICAgICAgICAgICB0aGlzLndpbmRvd1dpZHRoID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsaWVudFdpZHRoCiAgICAgICAgfQogICAgICAgIC8vIOaWh+Wtl+mgreWDjwogICAgICAgIHRoaXMudGV4dEF2YXRhcih0aGlzLnVzZXIudXNlcm5hbWUpOwogICAgfQp9Cg=="}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6QA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views/user", "sourcesContent": ["<template>\n    <div class=\"main\">\n        <div class=\"left\" :style=\"{width:isCollapse?'64px':'200px',background:themeColor.bg,color:themeColor.color}\" style=\"transition: .3s;\">\n            <div class=\"logo\">\n<!--                <img src=\"@s/assets/logo.jpg\" style=\"width: 26%\">-->\n            </div>\n            <el-menu\n                    :collapse-transition=\"false\"\n                    :collapse=\"isCollapse\"\n                    :router=\"true\"\n                    :default-active=\"$route.path\"\n                    :background-color=\"themeColor.bg\"\n                    :text-color=\"themeColor.color\"\n                    :unique-opened=\"true\">\n                <el-menu-item index=\"/home/\">\n                    <i class=\"el-icon-s-home\"></i>\n                    <span>首页</span>\n                </el-menu-item>\n\n                <el-submenu index=\"1\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>任务管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>发布任务</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"2\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-paperclip\"></i>\n                        <span>订单管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已发布任务</span>\n                    </el-menu-item>\n\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"3\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>公告管理</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                        <span>查看公告</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"9\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>评价管理</span>\n                    </template>\n                     <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我发布的评价</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                    <span>查看评价</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <!-- 论坛入口，只对维修员和管理员可见，用户角色ID是14 -->\n                <el-submenu index=\"10\" v-if=\"user.role.id !== 14\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-chat-dot-square\"></i>\n                        <span>维修员论坛</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>/posts\">\n                        <i class=\"el-icon-document\"></i>\n                        <span>帖子列表</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/my-posts\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我的帖子</span>\n                    </el-menu-item>\n                    <!-- 管理员角色不是14（用户角色）也不是13（维修员角色） -->\n                    <el-menu-item v-if=\"user.role.id !== 14 && user.role.id !== 13\" index=\"/home/<USER>/audit\">\n                        <i class=\"el-icon-s-check\"></i>\n                        <span>帖子审核</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/notifications\">\n                        <i class=\"el-icon-bell\"></i>\n                        <span>消息通知</span>\n                        <el-badge v-if=\"unreadCount > 0\" :value=\"unreadCount\" class=\"notification-badge\" />\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"11\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>个人中心</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">个人信息\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-menu-item v-if=\"user.role.id === 14\">\n                    <span>当前余额: {{ user.balance }}元</span>\n                    <!-- <el-button type=\"text\" @click=\"recharge(user.studentId)\">充值余额</el-button> -->\n                </el-menu-item>\n            </el-menu>\n        </div>\n\n        <div class=\"right\"\n             :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px'}\">\n            <div class=\"top\"\n                 :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px',background:themeColor.bg}\">\n                <div class=\"icon\" @click=\"isCollapse = !isCollapse\">\n                    <i :class=\"isCollapse?'el-icon-s-unfold':'el-icon-s-fold'\" :style=\"{color:themeColor.color}\"></i>\n                </div>\n                <el-menu\n\n                        :unique-opened=\"true\"\n                        :default-active=\"activeIndex\"\n                        class=\"el-menu-demo\"\n                        mode=\"horizontal\"\n                        :background-color=\"themeColor.bg\"\n                        :text-color=\"themeColor.color\"\n                        :active-text-color=\"themeColor.color\"\n                        menu-trigger=\"click\">\n\n                    <el-menu-item v-if=\"user.role.id === 14\" @click=\"recharge(user.studentId)\">充值余额</el-menu-item>\n<!--                    <el-menu-item @click=\"recharge(user.studentId)\">充值余额</el-menu-item>-->\n\n                    <el-submenu index=\"1\">\n                        <template slot=\"title\">更换主题</template>\n                        <el-menu-item v-for=\"item in theme\" @click=\"changeColor(item)\">\n                            {{item.name}}\n                        </el-menu-item>\n                    </el-submenu>\n                    <el-submenu index=\"2\">\n<!--                        <template slot=\"title\">{{user.username}}</template>-->\n                        <el-avatar slot=\"title\" style=\"background: #65c4a6; user-select: none;\">{{firstName}}</el-avatar>\n                        <el-menu-item index=\"2-1\" @click=\"exit\">退出</el-menu-item>\n                        <el-menu-item index=\"2-2\" @click=\"updPassword(user.id)\">修改密码</el-menu-item>\n                        <el-menu-item index=\"2-3\" @click=\"personalInformation()\">修改个人信息</el-menu-item>\n                    </el-submenu>\n                </el-menu>\n\n            </div>\n            <div class=\"bottom\">\n                <div class=\"bottom_top\">\n                    <el-breadcrumb separator-class=\"el-icon-arrow-right\">\n                        <el-breadcrumb-item v-for=\"item in breadList\" :to=\"item.path\" v-if=\"item.meta.title\">\n                            {{item.meta.title}}\n                        </el-breadcrumb-item>\n                    </el-breadcrumb>\n                </div>\n                <transition name=\"el-fade-in\" mode=\"out-in\">\n                    <router-view @personalInformation=\"personalInformation\"></router-view>\n                </transition>\n            </div>\n        </div>\n\n        <el-drawer\n                title=\"完善信息\"\n                :visible.sync=\"drawer\"\n                direction=\"rtl\"\n                closeDrawer=\"false\"\n                :show-close=\"false\"\n                :before-close=\"handleClose\">\n            <el-form :model=\"ruleForm\" status-icon :rules=\"rules\" ref=\"ruleForm\" label-width=\"100px\"\n                     class=\"demo-ruleForm ruleform\">\n\n                <!-- 根据角色 ID 动态显示班级信息 -->\n                <el-form-item label=\"类别\" v-if=\"user.role.id !== 14\">\n                    <el-cascader\n                            v-model=\"value\"\n                            :options=\"role.depts\"\n                            :props=\"{\n                children:'classes',\n                label:'name',\n                value:'id'\n            }\"\n                    ></el-cascader>\n                </el-form-item>\n\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"工号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\"></el-input>\n                </el-form-item>\n\n                <el-form-item label=\"性别\">\n                    <el-radio-group v-model=\"sex\">\n                        <el-radio label=\"0\">男</el-radio>\n                        <el-radio label=\"1\">女</el-radio>\n                    </el-radio-group>\n                </el-form-item>\n\n                <el-form-item>\n                    <el-button type=\"primary\" @click=\"submitForm('ruleForm')\">提交</el-button>\n                </el-form-item>\n            </el-form>\n        </el-drawer>\n\n        <el-dialog title=\"修改信息\" :visible.sync=\"dialogVisible\" :close-on-click-modal=\"false\">\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model.number=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"手机号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\" oninput=\"if(value.length>11)value=value.slice(0,11)\"></el-input>\n                </el-form-item>\n\n                <!-- 维修员可以修改类别和子类别 -->\n                <template v-if=\"user.role && user.role.id === 13\">\n                    <el-form-item label=\"类别-子类别\" prop=\"categoryValue\">\n                        <el-cascader\n                            v-model=\"categoryValue\"\n                            :options=\"categoryOptions\"\n                            :props=\"{\n                                children: 'children',\n                                label: 'name',\n                                value: 'id',\n                                checkStrictly: false\n                            }\"\n                            placeholder=\"请选择类别和子类别\"\n                            clearable\n                        ></el-cascader>\n                    </el-form-item>\n\n                    <!-- 维修员地址信息 -->\n                    <el-form-item label=\"省市区\" prop=\"addressRegion\">\n                        <el-cascader\n                            v-model=\"addressRegion\"\n                            :options=\"regionOptions\"\n                            placeholder=\"请选择省/市/区\"\n                            style=\"width: 100%\"\n                        ></el-cascader>\n                    </el-form-item>\n                    <el-form-item label=\"详细地址\" prop=\"address\">\n                        <el-input\n                            v-model=\"ruleForm.address\"\n                            type=\"textarea\"\n                            placeholder=\"请输入详细地址信息，如街道、门牌号等\"\n                            :rows=\"3\"\n                        ></el-input>\n                    </el-form-item>\n                </template>\n\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"dialogVisible = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"submitChanges\">确 定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\n    import {mapState, mapMutations} from \"vuex\"\n    import user from \"@s/store/module/user\";\n    import regionData from '@/assets/data/region.js';\n\n    export default {\n        name: \"Home\",\n        methods: {\n            ...mapMutations('user', ['setUser']),\n            changeColor(val){\n                sessionStorage.setItem(\"themeColor\",JSON.stringify(val))\n                this.themeColor = {'bg':val.value,'color':val.color};\n            },\n            //面包屑\n            getBreadcrumb() {\n                let matched = this.$route.matched;\n                if (matched[0].name != 'home') {\n                    matched = [{path: \"/home/\", meta: {title: '首页'}}].concat(matched)\n                }\n                this.breadList = matched;\n            },\n            //关闭抽屉触发的事件\n            handleClose(done) {\n                // 如果是首次登录且未完善信息，不允许关闭抽屉\n                if (this.user.state === 0) {\n                    this.$msg(\"首次登录必须完善信息\", \"error\")\n                } else {\n                    // 如果不是首次登录或已完善信息，允许关闭抽屉\n                    done()\n                }\n            },\n            submitForm(formName) {\n                this.$refs[formName].validate((valid) => {\n                    if (valid) {\n                        // 根据角色ID决定是否需要选择类别\n                        if ((this.user.role.id !== 14 && this.value) || this.user.role.id === 14) {\n                            const userData = {\n                                id: this.user.id,\n                                deptId: this.user.role.id !== 14 ? this.value[0] : null,\n                                // classId 字段在数据库中不存在，移除该字段\n                                username: this.ruleForm.username,\n                                phone: this.ruleForm.phone,\n                                sex: this.sex,\n                                // 设置状态为已完善信息\n                                state: 1\n                            }\n\n                            this.$put(\"/user\", userData)\n                                .then(res => {\n                                    if (res.data.status) {\n                                        this.drawer = false;\n                                        this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                                        // 更新本地用户信息\n                                        if (res.data.user) {\n                                            sessionStorage.setItem('user', JSON.stringify(res.data.user))\n                                            this.setUser(res.data.user)\n                                        }\n                                        this.newList(this.user.id)\n                                    } else {\n                                        this.$msg(res.data.msg, \"error\")\n                                    }\n                                })\n                                .catch(err => {\n                                    console.error('Update failed:', err)\n                                    this.$msg(err.response?.data?.msg || \"更新失败，请稍后重试\", \"error\")\n                                })\n                        } else {\n                            this.$notifyMsg(\"错误\", \"请选择类别\", \"error\")\n                        }\n                    } else {\n                        return false;\n                    }\n                });\n            },\n            //修改密码\n            updPassword(id) {\n                this.$prompt('请输入密码', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputType: 'password',\n                    closeOnClickModal:false,\n                    inputPattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,\n                    inputErrorMessage: '格式不对，密码只能输入6-16位英文和数字'\n                }).then((res) => {\n                    // console.log(res);\n                    this.$put('/user', {id: id, password: res.value})\n                    .then((res) => {\n                        this.$notifyMsg('成功', res.data.msg, 'success')\n                    })\n                }).catch(() => {\n                })\n            },\n            recharge(studentId) {\n                this.$prompt('请输入充值金额', '充值', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputPattern: /^(0\\.\\d{1,2}|[1-9]\\d*(\\.\\d{1,2})?)$/,\n                    inputErrorMessage: '请输入有效金额（最多两位小数）'\n                }).then(({ value }) => {\n                    const amount = parseFloat(value);\n\n                    // 验证金额范围\n                    if (amount < 0.01 || amount > 10000) {\n                        this.$msg(\"充值金额必须在0.01-10000元之间\", \"error\");\n                        return;\n                    }\n\n                    // 创建支付宝支付订单\n                    const payData = {\n                        subject: '校园帮账户充值',\n                        totalAmount: amount,\n                        userId: this.user.id\n                    };\n\n                    // 调用后端创建支付订单接口\n                    this.$post('api/alipay/create', payData)\n                        .then(res => {\n                            if (res.data.status) {\n                                // 在新窗口中打开支付页面\n                                const payWindow = window.open('', '_blank', 'width=800,height=600');\n                                payWindow.document.write(res.data.payForm);\n                                payWindow.document.close();\n\n                                // 定义轮询次数和间隔\n                                let pollCount = 0;\n                                const maxPolls = 60; // 最多轮询60次\n                                const pollInterval = 3000; // 每3秒轮询一次\n\n                                // 获取订单号（从响应中获取）\n                                const orderNo = res.data.orderNo; // 假设后端返回了orderNo\n\n                                // 开始轮询支付状态\n                                const checkPayment = setInterval(() => {\n                                    // 如果支付窗口关闭或达到最大轮询次数，停止轮询\n                                    if (pollCount >= maxPolls || payWindow.closed) {\n                                        clearInterval(checkPayment);\n                                        \n                                        // 最后查询一次支付状态\n                                        this.$get(`api/alipay/query/${orderNo}`)\n                                            .then(queryRes => {\n                                                if (queryRes.data.status && queryRes.data.data.status === 1) {\n                                                    // 支付成功\n                                                    this.newList(this.user.id);\n                                                    this.$msg(\"充值成功\", \"success\");\n                                                } else {\n                                                    // 支付未完成\n                                                    this.$msg(\"支付未完成，如已支付请稍后刷新查看\", \"warning\");\n                                                }\n                                            })\n                                            .catch(() => {\n                                                this.$msg(\"查询支付状态失败，如已支付请稍后刷新查看\", \"warning\");\n                                            });\n                                        return;\n                                    }\n\n                                    // 定期查询支付状态\n                                    this.$get(`api/alipay/query/${orderNo}`)\n                                        .then(queryRes => {\n                                            if (queryRes.data.status && queryRes.data.data.status === 1) {\n                                                // 支付成功\n                                                clearInterval(checkPayment);\n                                                this.newList(this.user.id);\n                                                this.$msg(\"充值成功\", \"success\");\n                                                if (!payWindow.closed) {\n                                                    payWindow.close(); // 自动关闭支付窗口\n                                                }\n                                            }\n                                        })\n                                        .catch(err => {\n                                            console.error('查询支付状态失败:', err);\n                                        });\n\n                                    pollCount++;\n                                }, pollInterval);\n\n                            } else {\n                                this.$msg(res.data.msg || \"创建支付订单失败\", \"error\");\n                            }\n                        })\n                        .catch(err => {\n                            console.error('创建支付订单失败:', err);\n                            this.$msg(\"创建支付订单失败，请稍后重试\", \"error\");\n                        });\n                }).catch(() => {\n                    this.$msg(\"已取消充值\", \"info\");\n                });\n            },\n            personalInformation() {\n                this.dialogVisible = true;\n                this.ruleForm.username = this.user.username;\n                this.ruleForm.phone = this.user.phone;\n\n                // 如果是维修员，加载类别和子类别数据\n                if (this.user.role && this.user.role.id === 13) {\n                    // 设置当前的类别和子类别\n                    if (this.user.dept) {\n                        this.ruleForm.deptId = this.user.dept.id;\n                    }\n                    if (this.user.type) {\n                        this.ruleForm.classId = this.user.type.id;\n                    }\n\n                    // 设置级联选择器的初始值\n                    if (this.user.dept && this.user.type) {\n                        this.categoryValue = [this.user.dept.id, this.user.type.id];\n                    } else if (this.user.dept) {\n                        this.categoryValue = [this.user.dept.id];\n                    } else {\n                        this.categoryValue = [];\n                    }\n\n                    // 设置地址选择器的初始值\n                    if (this.user.province && this.user.city && this.user.district) {\n                        this.addressRegion = [this.user.province, this.user.city, this.user.district];\n                        this.ruleForm.address = this.user.address || '';\n                    } else {\n                        this.addressRegion = [];\n                        this.ruleForm.address = '';\n                    }\n\n                    // 加载所有可用的类别和子类别\n                    this.loadCategoryOptions();\n                }\n            },\n\n            // 加载类别和子类别的级联选项\n            loadCategoryOptions() {\n                // 先获取所有类别\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        // 保存原始类别列表（兼容性考虑）\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n\n                        // 为每个类别加载子类别\n                        const promises = res.data.dept.map(dept => {\n                            return this.$get(\"/class/list\", { deptId: dept.id })\n                            .then(classRes => {\n                                if (classRes.data.status && classRes.data.class) {\n                                    // 返回带有子类别的类别对象\n                                    return {\n                                        ...dept,\n                                        children: classRes.data.class\n                                    };\n                                }\n                                // 如果没有子类别，返回原始类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            })\n                            .catch(() => {\n                                // 出错时返回没有子类别的类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            });\n                        });\n\n                        // 等待所有子类别加载完成\n                        Promise.all(promises)\n                        .then(categories => {\n                            this.categoryOptions = categories;\n                            console.log('级联选择器选项:', this.categoryOptions);\n                        });\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadDepartments() {\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadSubCategories(deptId) {\n                if (!deptId) return;\n\n                this.$get(\"/class/list\", { deptId: deptId })\n                .then(res => {\n                    if (res.data.status && res.data.class) {\n                        this.classOptions = res.data.class;\n                        console.log('可用的子类别列表:', this.classOptions);\n\n                        // 如果当前选择的子类别不在新的子类别列表中，清空选择\n                        const exists = this.classOptions.some(item => item.id === this.ruleForm.classId);\n                        if (!exists) {\n                            this.ruleForm.classId = null;\n                        }\n                    } else {\n                        this.classOptions = [];\n                        this.ruleForm.classId = null;\n                        this.$msg(\"获取子类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取子类别列表失败:', err);\n                    this.classOptions = [];\n                    this.ruleForm.classId = null;\n                    this.$msg(\"获取子类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n            // submitChanges(){\n            //     this.$put(\"/user\",{id:this.user.id,username:this.ruleForm.username,phone:this.ruleForm.phone})\n            //     .then(res=>{\n            //         this.$notifyMsg(\"成功\",res.data.msg,\"success\",1000);\n            //         this.dialogVisible = false;\n            //         this.newList(this.user.id)\n            //     })\n            // },\n            submitChanges() {\n                // 准备要更新的用户数据\n                const userData = {\n                    id: this.user.id,\n                    username: this.ruleForm.username,\n                    phone: this.ruleForm.phone,\n                    state: 1\n                };\n\n                // 如果是维修员，添加类别和子类别信息\n                if (this.user.role && this.user.role.id === 13) {\n                    // 从级联选择器中获取类别和子类别ID\n                    if (this.categoryValue && this.categoryValue.length > 0) {\n                        // 第一个值是类别ID\n                        userData.deptId = this.categoryValue[0];\n\n                        // 如果选择了子类别（数组长度大于1），第二个值是子类别ID\n                        if (this.categoryValue.length > 1) {\n                            userData.classId = this.categoryValue[1];\n                        }\n                    }\n                    // 兼容旧版本的选择方式\n                    else {\n                        // 只有当选择了类别时才更新类别ID\n                        if (this.ruleForm.deptId) {\n                            userData.deptId = this.ruleForm.deptId;\n                        }\n\n                        // 只有当选择了子类别时才更新子类别ID\n                        if (this.ruleForm.classId) {\n                            userData.classId = this.ruleForm.classId;\n                        }\n                    }\n\n                    // 处理地址信息\n                    if (this.addressRegion && this.addressRegion.length === 3) {\n                        userData.province = this.addressRegion[0];\n                        userData.city = this.addressRegion[1];\n                        userData.district = this.addressRegion[2];\n                    }\n\n                    // 如果有详细地址，添加到更新数据中\n                    if (this.ruleForm.address) {\n                        userData.address = this.ruleForm.address;\n                    }\n                }\n\n                console.log('提交的用户数据:', userData);\n\n                // 发送更新请求\n                this.$put(\"/user\", userData)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\");\n                            this.dialogVisible = false;\n                            this.newList(this.user.id);\n                        } else {\n                            this.$msg(res.data.msg || \"更新失败\", \"error\");\n                        }\n                    })\n                    .catch(err => {\n                        console.error('更新用户信息失败:', err);\n                        this.$msg(\"更新失败，请稍后重试\", \"error\");\n                    });\n            },\n            //根据当前用户查询id\n            newList(id) {\n                this.$get(\"/user/\" + id)\n                .then((rs) => {\n                    // 确保用户状态正确更新\n                    if (rs.data.user) {\n                        console.log('User data from API:', JSON.stringify(rs.data.user, null, 2));\n                        console.log('User role ID:', rs.data.user.role ? rs.data.user.role.id : 'No role');\n                        console.log('User dept:', rs.data.user.dept ? JSON.stringify(rs.data.user.dept, null, 2) : 'No dept data');\n                        console.log('User type:', rs.data.user.type ? JSON.stringify(rs.data.user.type, null, 2) : 'No type data');\n\n                        // 如果是维修员但没有type信息，尝试重新获取用户数据\n                        if (rs.data.user.role && rs.data.user.role.id === 13 && !rs.data.user.type) {\n                            console.log('维修员没有type信息，尝试修复...');\n                            // 尝试手动设置type信息\n                            if (rs.data.user.classId) {\n                                console.log('找到classId:', rs.data.user.classId);\n                                // 获取type信息\n                                this.$get(\"/class/\" + rs.data.user.classId)\n                                .then(typeRes => {\n                                    if (typeRes.data.class) {\n                                        console.log('获取到type信息:', JSON.stringify(typeRes.data.class, null, 2));\n                                        // 手动设置type信息\n                                        rs.data.user.type = typeRes.data.class;\n                                        // 更新用户信息\n                                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user));\n                                        this.setUser(rs.data.user);\n                                    }\n                                });\n                            }\n                        }\n\n                        // 如果用户已完善信息，确保状态为1\n                        if (rs.data.user.dept && rs.data.user.state === 0) {\n                            rs.data.user.state = 1;\n                            // 更新用户状态\n                            this.$put(\"/user\", {\n                                id: rs.data.user.id,\n                                state: 1\n                            });\n                        }\n                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user))\n                        this.setUser(JSON.parse(sessionStorage.getItem(\"user\")))\n                        // 修改完名字, 清空當前firstName; 避免出現疊加\n                        this.firstName = '';\n                        this.textAvatar(rs.data.user.username);\n                    }\n                })\n            },\n            exit(){\n                sessionStorage.removeItem('user');\n                this.$router.push('/')\n            },\n            // 文字頭像\n            textAvatar(username) {\n                let arr = username.split(' ');\n                for (var i in arr) {\n                    this.firstName += arr[i].substr(0,1);\n                }\n                this.firstName = this.firstName.toLocaleUpperCase();\n                console.log('firstName->' + this.firstName);\n            },\n\n            // 获取未读通知数量\n            getUnreadNotificationCount() {\n                if (this.user && this.user.id) {\n                    this.$get('/forum/notification/unread/count', { userId: this.user.id })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.unreadCount = res.data.count;\n                        }\n                    })\n                    .catch(err => {\n                        console.error('获取未读通知数量失败:', err);\n                    });\n                }\n            }\n        },\n        computed: {\n            ...mapState('user', ['user']),\n            theme(){\n                return this.$store.state.theme.theme\n            },\n        },\n        data() {\n            var validateUsername = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入姓名'));\n                } else {\n                    callback();\n                }\n            };\n            var validatePhone = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入手机号'));\n                } else {\n                    callback();\n                }\n            };\n\n            return {\n                // 文字头像\n                firstName:'',\n                ruleForm: {\n                    username: '',\n                    phone: '',\n                    deptId: null,\n                    classId: null,\n                    address: ''\n                },\n                // 级联选择器的值和选项\n                categoryValue: [],\n                categoryOptions: [],\n                // 保留原有的选项（兼容性考虑）\n                deptOptions: [],\n                classOptions: [],\n\n                // 地址相关\n                addressRegion: [],\n                regionOptions: [],\n                rules: {\n                    username: [\n                        {validator: validateUsername, trigger: 'blur'}\n                    ],\n                    phone: [\n                        {validator: validatePhone, trigger: 'blur'}\n                    ]\n                },\n                //颜色\n                themeColor : {'bg':'#fff','color':'#000'},\n                //性别\n                sex:'0',\n                drawer: false,\n                //当前路由\n                breadList: [],\n                //当前屏幕宽度\n                windowWidth: document.documentElement.clientWidth,\n                activeIndex: '1',\n                //控制菜单是否展开\n                isCollapse: false,\n                admin: \"\",\n                // role: [],\n                role:[],\n                //级联选择器的值\n                value: \"\",\n                dialogVisible:false,\n                // 未读通知数量\n                unreadCount: 0\n            }\n        },\n        watch: {\n            '$route'(to, form) {\n                this.getBreadcrumb()\n            }\n        },\n        created() {\n            // 初始化地址数据\n            this.regionOptions = regionData;\n\n            let theme = JSON.parse(sessionStorage.getItem(\"themeColor\"));\n            if (theme){\n                this.themeColor = {'bg':theme.value,'color':theme.color}\n            }\n\n            if (sessionStorage.getItem('user')){\n                this.getBreadcrumb();\n                const userData = JSON.parse(sessionStorage.getItem(\"user\"));\n                console.log('User data from session:', userData);\n                this.setUser(userData);\n\n                // 如果是维修员或管理员，获取未读通知数量\n                if (this.user.role && (this.user.role.id === 13 || this.user.role.id === 14)) {\n                    this.getUnreadNotificationCount();\n\n                    // 每分钟获取一次未读通知数量\n                    setInterval(() => {\n                        this.getUnreadNotificationCount();\n                    }, 60000);\n                }\n                //\n                // 检查用户是否是首次登录（通过检查state字段）\n                // state为0表示未完善信息，state为1表示已完善信息\n                if (this.user.state === 0) {\n                    this.$get(\"/role/\" + this.user.role.id)\n                    .then((res) => {\n                        console.log(res.data)\n                        this.role = res.data.role;\n                        this.drawer = true\n                        this.$msg(\"首次登录，请完善信息\", \"warning\")\n                    })\n                }\n            }else {\n                this.$msg(\"您向未登陆,没有权限\",\"error\")\n                this.$router.push(\"/\")\n            }\n\n        },\n        mounted() {\n            // 获取当前屏幕宽度\n            window.onresize = () => {\n                this.windowWidth = document.documentElement.clientWidth\n            }\n            // 文字頭像\n            this.textAvatar(this.user.username);\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .main {\n        display: flex;\n        height: 90%;\n\n        .left {\n            position: fixed;\n            height: 100%;\n\n            .logo {\n                width: 90%;\n                /*color: white;*/\n                font-size: 16px;\n                text-align: center;\n                padding: 8px 0;\n                /*border: 1px solid white;*/\n                margin: 9.1px auto 0 auto;\n            }\n        }\n\n        .right {\n            transition: all 0.3s ease 0s;\n            position: relative;\n\n            .top {\n                transition: all 0.3s ease 0s;\n                position: fixed;\n                /*color: #fff;*/\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                z-index: 9;\n\n                .icon {\n                    font-size: 20px;\n                    cursor: pointer;\n                    margin-left: 10px;\n                }\n            }\n\n            .bottom {\n                width: 100%;\n                height: 100%;\n                /*background: #fff;*/\n                margin-top: 65px;\n                .bottom_top {\n                    padding: 20px;\n                }\n            }\n        }\n\n        .ruleform /deep/ .el-input {\n            width: 80% !important;\n        }\n\n        /deep/ .el-cascader {\n            width: 100% !important;\n        }\n    }\n</style>\n"]}]}