<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8c4c7700-9702-4885-9612-8d8f079e5d72" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectId" id="2rwlYvCuuR7KfyQ7wOQNRqP4s6T" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/ending/2.15Matain&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.47575057&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;815d73b64a7c90c5de1b8d7a8a79ba0e&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Program Files\\JetBrains\\IntelliJ IDEA 2022.3\\plugins\\javascript-impl\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager" selected="Spring Boot.CampusGangApplication">
    <configuration name="campus_help.sql" type="DatabaseScript" editBeforeRun="true" temporary="true" nameIsGenerated="true">
      <script-file value="$PROJECT_DIR$/src/main/resources/sql/campus_help.sql" />
      <script-mode>FILE</script-mode>
      <method v="2" />
    </configuration>
    <configuration name="CampusGangApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="250426" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yqn.CampusGangApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CampusGangApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="250426" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yqn.CampusGangApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="数据库脚本.campus_help.sql" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8c4c7700-9702-4885-9612-8d8f079e5d72" name="更改" comment="" />
      <created>1737478072602</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1737478072602</updated>
      <workItem from="1737478074012" duration="690000" />
      <workItem from="1737563544566" duration="7292000" />
      <workItem from="1737796940941" duration="9286000" />
      <workItem from="1737872308472" duration="7327000" />
      <workItem from="1737911851865" duration="6937000" />
      <workItem from="1738861724284" duration="1015000" />
      <workItem from="1739095721794" duration="2993000" />
      <workItem from="1739293730658" duration="486000" />
      <workItem from="1739359811936" duration="7129000" />
      <workItem from="1739463806401" duration="8497000" />
      <workItem from="1739555010677" duration="2693000" />
      <workItem from="1739557716584" duration="615000" />
      <workItem from="1739558357648" duration="84000" />
      <workItem from="1739558601795" duration="1668000" />
      <workItem from="1739560546486" duration="215000" />
      <workItem from="1739560777223" duration="27537000" />
      <workItem from="1739627089157" duration="25986000" />
      <workItem from="1740053861247" duration="5445000" />
      <workItem from="1742571944149" duration="10898000" />
      <workItem from="1744190129685" duration="61000" />
      <workItem from="1744191256633" duration="745000" />
      <workItem from="1744194090579" duration="6483000" />
      <workItem from="1744455691830" duration="1475000" />
      <workItem from="1745132408934" duration="8091000" />
      <workItem from="1745691417244" duration="11065000" />
      <workItem from="1748375646055" duration="2917000" />
      <workItem from="1748416448046" duration="2991000" />
      <workItem from="1748541438515" duration="611000" />
      <workItem from="1748625876744" duration="4868000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>