package com.yqn.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yqn.pojo.User;
// import com.yqn.pojo.School;
import com.yqn.service.UserService;
import com.yqn.common.tools.MessageTools;
import com.yqn.common.tools.PocketMoney;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class UserController {
    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    @PostMapping(value = "/user", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<User> createUser(@RequestBody User user) {
        // 处理逻辑
        return ResponseEntity.ok(user);
    }
    @Autowired
    private UserService userService;
    @Autowired
    private MessageTools message;
    @Autowired
    private PocketMoney money;

    // 检查登录
    @GetMapping("/login")
    public Map<String, Object> checkUserLogin(User checkUser, HttpSession session) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("student_id", checkUser.getStudentId())
                .eq("password", checkUser.getPassword())
                .eq("role_id", checkUser.getRoleId());
        User user = userService.getOne(wrapper);

        if (user != null) {
            session.setAttribute("user", user);
            return message.message(true, "请求成功", "user", user);
        }
        return message.message(false, "账号或密码错误", "", null);
    }

    // 获取全部User
    @GetMapping
    public Map<String, Object> users(String studentId, String username) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        if (studentId != null) {
            wrapper.eq("student_id", studentId);
            return message.message(true, "请求成功", "user", userService.list(wrapper));
        }
        if (username != null) {
            wrapper.like("username", username);
            return message.message(true, "请求成功", "user", userService.list(wrapper));
        }
        List<User> users = userService.list();
        return message.message(true, "请求成功", "user", users);
    }

    // 根据id获取User
    @GetMapping("/{id}")
    public Map<String, Object> user(@PathVariable String id) {
        User user = userService.getById(id);
        return message.message(true, "请求成功", "user", user);
    }
//    @GetMapping("/{id}")
//    public Map<String, Object> user(@PathVariable String id) {
//        // 避免多次调用相同的查询方法
//        User user = userService.getById(id);
//        School school = user.getSchool();
//        // 其他业务逻辑
//        return message.message(true, "请求成功", "user", user);
//    }
    // 添加User
    @PostMapping
    public Map<String, Object> saveUser(@RequestBody User user) {
        try {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("role_id", user.getRoleId())
                    .eq("student_id", user.getStudentId());
            User one = userService.getOne(wrapper);
            if (one == null) {
                // 设置默认值
                user.setBalance(0.0);  // 设置初始余额
                user.setState(0);      // 设置初始状态
                user.setCreateTime(new Date());  // 设置创建时间

                boolean saved = userService.save(user);
                if (saved) {
                    return message.message(true, "注册成功", "", null);
                }
                return message.message(false, "注册失败", "", null);
            }
            return message.message(false, "该手机号已被注册", "", null);
        } catch (Exception e) {
            log.error("用户注册失败", e);
            return message.message(false, "注册失败：" + e.getMessage(), "", null);
        }
    }

    // 更新信息
    @PutMapping
    public Map<String, Object> putUser(@RequestBody User user) {
        try {
            // 获取原有用户信息
            User existingUser = userService.getById(user.getId());
            if (existingUser == null) {
                return message.message(false, "用户不存在", "", null);
            }

            // 保留原有的一些字段值
            user.setStudentId(existingUser.getStudentId());
            user.setPassword(existingUser.getPassword());
            user.setBalance(existingUser.getBalance());
            // 如果前端没有传递state值，则使用原有的state值
            if (user.getState() == null) {
                user.setState(existingUser.getState());
            }
            user.setCreateTime(existingUser.getCreateTime());

            boolean update = userService.updateById(user);
            if (update) {
                // 返回更新后的完整用户信息
                User updatedUser = userService.getById(user.getId());
                return message.message(true, "更新信息成功", "user", updatedUser);
            }
            return message.message(false, "更新信息失败", "", null);
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return message.message(false, "更新失败：" + e.getMessage(), "", null);
        }
    }

    // 删除学生
    @DeleteMapping("/{id}")
    public Map<String, Object> delUser(@PathVariable Long id) {
        boolean remove = userService.removeById(id);
        if (remove) {
            return message.message(true, "删除学生成功", "", null);
        }
        return message.message(false, "error,删除学生失败", "", null);
    }

    // 零钱转入
    @PutMapping("/rollIn")
    public Map<String, Object> rollIn(@RequestParam("studentId") String studentId, @RequestParam("balance") Double balance) {
        try {
            // 先检查用户是否存在，同时确保是学校ID为14的用户
            QueryWrapper<User> checkWrapper = new QueryWrapper<>();
            checkWrapper.eq("student_id", studentId)
                       .eq("role_id", 14L);  // 只允许学校ID为14的用户充值
            User user = userService.getOne(checkWrapper);

            if (user == null) {
                return message.message(false, "用户不存在或无充值权限", "", null);
            }

            if (balance <= 0) {
                return message.message(false, "充值金额必须大于0", "", null);
            }

            // 执行充值
            UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("student_id", studentId)
                        .eq("role_id", 14L)  // 确保只更新学校ID为14的用户
                        .setSql("balance = balance + " + balance);

            boolean success = userService.update(updateWrapper);
            if (success) {
                // 获取更新后的用户信息
                User updatedUser = userService.getById(user.getId());
                return message.message(true, "充值成功", "user", updatedUser);
            }
            return message.message(false, "充值失败", "", null);
        } catch (Exception e) {
            log.error("充值失败", e);
            return message.message(false, "充值失败：" + e.getMessage(), "", null);
        }
    }

    // 零钱转出
    @PutMapping("rollOut")
    public Map<String, Object> rollOut(String studentId, Double balance) {
        return money.transfer("balance=balance-", balance, studentId);
    }

    /**
     * 获取用户最新信息（包括余额）
     */
    @GetMapping("/info/{id}")
    public Map<String, Object> getUserInfo(@PathVariable Long id) {
        try {
            User user = userService.getById(id);
            if (user != null) {
                return message.message(true, "获取用户信息成功", "user", user);
            } else {
                return message.message(false, "用户不存在", "", null);
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return message.message(false, "获取用户信息失败：" + e.getMessage(), "", null);
        }
    }
}
