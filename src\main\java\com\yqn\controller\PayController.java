package com.yqn.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yqn.common.tools.MessageTools;
import com.yqn.common.utils.PayUtil;
import com.yqn.pojo.RechargeRecord;
import com.yqn.pojo.User;
import com.yqn.service.RechargeRecordService;
import com.yqn.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * 支付控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/alipay")
public class PayController {

    @Autowired
    private PayUtil payUtil;

    @Autowired
    private UserService userService;

    @Autowired
    private RechargeRecordService rechargeRecordService;

    @Autowired
    private MessageTools messageTools;

    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    public Map<String, Object> createPayment(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            Double totalAmount = Double.valueOf(params.get("totalAmount").toString());
            String subject = params.get("subject").toString();

            // 验证用户是否存在
            User user = userService.getById(userId);
            if (user == null) {
                return messageTools.message(false, "用户不存在", "", null);
            }

            // 验证金额
            if (totalAmount <= 0 || totalAmount > 10000) {
                return messageTools.message(false, "充值金额必须在0.01-10000元之间", "", null);
            }

            // 生成订单号
            String orderNo = payUtil.generateOrderNo(userId);

            // 创建充值记录
            RechargeRecord record = new RechargeRecord();
            record.setUserId(userId);
            record.setOrderNo(orderNo);
            record.setAmount(totalAmount);
            record.setStatus(0); // 待支付
            record.setPaymentMethod("alipay");
            record.setCreateTime(new Date());
            record.setRemark("支付宝充值");

            boolean saved = rechargeRecordService.save(record);
            if (!saved) {
                return messageTools.message(false, "创建充值记录失败", "", null);
            }

            // 调用支付宝接口创建支付订单
            String payForm = payUtil.sendRequestToAlipay(orderNo, totalAmount.floatValue(), subject);

            return messageTools.message(true, "创建支付订单成功", "payForm", payForm);

        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            return messageTools.message(false, "创建支付订单失败：" + e.getMessage(), "", null);
        }
    }

    /**
     * 支付宝异步通知
     */
    @PostMapping("/notify")
    public String handleNotify(HttpServletRequest request) {
        try {
            log.info("收到支付宝异步通知");

            // 验证签名
            boolean signVerified = payUtil.verifySignature(request);
            if (!signVerified) {
                log.error("支付宝异步通知签名验证失败");
                return "fail";
            }

            // 获取回调参数
            Map<String, String> params = payUtil.getCallbackParams(request);

            String outTradeNo = params.get("out_trade_no"); // 商户订单号
            String tradeNo = params.get("trade_no"); // 支付宝交易号
            String tradeStatus = params.get("trade_status"); // 交易状态
            String totalAmount = params.get("total_amount"); // 交易金额

            log.info("支付宝异步通知：订单号={}, 交易号={}, 状态={}, 金额={}",
                    outTradeNo, tradeNo, tradeStatus, totalAmount);

            // 查询充值记录
            QueryWrapper<RechargeRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("order_no", outTradeNo);
            RechargeRecord record = rechargeRecordService.getOne(wrapper);

            if (record == null) {
                log.error("充值记录不存在，订单号：{}", outTradeNo);
                return "fail";
            }

            // 验证金额
            if (!record.getAmount().equals(Double.valueOf(totalAmount))) {
                log.error("金额不匹配，订单号：{}，记录金额：{}，通知金额：{}",
                        outTradeNo, record.getAmount(), totalAmount);
                return "fail";
            }

            // 处理支付成功
            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                if (record.getStatus() == 0) { // 只处理待支付状态的订单
                    // 更新充值记录状态
                    UpdateWrapper<RechargeRecord> recordWrapper = new UpdateWrapper<>();
                    recordWrapper.eq("order_no", outTradeNo)
                               .set("status", 1)
                               .set("trade_no", tradeNo)
                               .set("pay_time", new Date());
                    rechargeRecordService.update(recordWrapper);

                    // 更新用户余额
                    UpdateWrapper<User> userWrapper = new UpdateWrapper<>();
                    userWrapper.eq("id", record.getUserId())
                              .setSql("balance = balance + " + record.getAmount());
                    userService.update(userWrapper);

                    log.info("充值成功，用户ID：{}，金额：{}", record.getUserId(), record.getAmount());
                }
            }

            return "success";

        } catch (Exception e) {
            log.error("处理支付宝异步通知异常", e);
            return "fail";
        }
    }

    /**
     * 支付宝同步返回
     */
    @GetMapping("/return")
    public void handleReturn(HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("收到支付宝同步返回");

            // 验证签名
            boolean signVerified = payUtil.verifySignature(request);

            if (signVerified) {
                Map<String, String> params = payUtil.getCallbackParams(request);
                String outTradeNo = params.get("out_trade_no");
                String tradeNo = params.get("trade_no");
                String totalAmount = params.get("total_amount");

                log.info("支付成功，订单号：{}，交易号：{}，金额：{}", outTradeNo, tradeNo, totalAmount);

                // 重定向到成功页面
                response.sendRedirect("http://localhost:8848/#/home?payment=success&orderNo=" + outTradeNo);
            } else {
                log.error("支付宝同步返回签名验证失败");
                response.sendRedirect("http://localhost:8848/#/home?payment=fail");
            }

        } catch (IOException e) {
            log.error("处理支付宝同步返回异常", e);
        }
    }

    /**
     * 查询支付状态
     */
    @GetMapping("/query/{orderNo}")
    public Map<String, Object> queryPaymentStatus(@PathVariable String orderNo) {
        try {
            // 查询充值记录
            QueryWrapper<RechargeRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("order_no", orderNo);
            RechargeRecord record = rechargeRecordService.getOne(wrapper);

            if (record == null) {
                return messageTools.message(false, "订单不存在", "", null);
            }

            // 如果订单已支付，直接返回结果
            if (record.getStatus() == 1) {
                return messageTools.message(true, "支付成功", "data", record);
            }

            // 调用支付宝接口查询支付状态
            String queryResult = payUtil.query(orderNo);
            log.info("支付宝查询结果：{}", queryResult);

            return messageTools.message(true, "查询成功", "data", record);

        } catch (Exception e) {
            log.error("查询支付状态失败", e);
            return messageTools.message(false, "查询失败：" + e.getMessage(), "", null);
        }
    }
}
