{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\MyProfile.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\MyProfile.vue", "mtime": 1748711742663}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MyProfile.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8GA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MyProfile.vue", "sourceRoot": "src/views/user/children", "sourcesContent": ["<template>\n    <div>\n        <el-card class=\"box-card\">\n            <div slot=\"header\" class=\"clearfix\">\n                <span>基本信息</span>\n                <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"$emit('personalInformation')\">修改信息</el-button>\n            </div>\n            <div class=\"content_txt\">\n                <el-collapse v-model=\"activeNames\">\n                    <el-collapse-item title=\"账号\" name=\"1\">\n                        <div>{{user.studentId}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"姓名\" name=\"2\">\n                        <div>{{user.username}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"手机号\" name=\"3\">\n                        <div>{{user.phone}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"角色\" name=\"4\">\n                        <div>{{user.role && user.role.name}}</div>\n                    </el-collapse-item>\n                    <!-- 用户角色的特定信息 -->\n                    <template v-if=\"user.role && user.role.id === 14\">\n                        <!-- 用户角色不需要显示类别信息 -->\n                    </template>\n                    <!-- 维修员角色的特定信息 -->\n                    <template v-else-if=\"user.role && user.role.id === 13\">\n                        <el-collapse-item v-if=\"user.dept\" title=\"类别\" name=\"5\">\n                            <div>{{user.dept.name}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item v-if=\"user.type\" title=\"子类别\" name=\"6\">\n                            <div>{{user.type.name}}</div>\n                        </el-collapse-item>\n                    </template>\n                    <!-- 其他角色的特定信息 -->\n                    <!-- <template v-else>\n                        <el-collapse-item v-if=\"user.dept\" title=\"部门\" name=\"5\">\n                            <div>{{user.dept.name}}</div>\n                        </el-collapse-item>\n                    </template> -->\n                    <!-- 维修员地址信息 -->\n                    <template v-if=\"user.role && user.role.id === 13\">\n                        <el-collapse-item title=\"地址信息\" name=\"7\">\n                            <div v-if=\"user.province && user.city && user.district\">\n                                <p>{{user.province}} {{user.city}} {{user.district}}</p>\n                                <!-- <p v-if=\"user.address\">详细地址: {{user.address}}</p> -->\n                            </div>\n                            <div v-else>\n\n                                <el-button type=\"text\" @click=\"showAddressSelector\">设置地址</el-button>\n                            </div>\n                        </el-collapse-item>\n                    </template>\n\n                    <el-collapse-item title=\"余额\" name=\"8\">\n                        <div><i class=\"el-icon-money\" style=\"color: red;\"> {{user.balance}}元</i></div>\n                    </el-collapse-item>\n                </el-collapse>\n            </div>\n        </el-card>\n\n        <!-- 子类别选择对话框 -->\n        <el-dialog title=\"选择子类别\" :visible.sync=\"typeDialogVisible\" width=\"30%\">\n            <el-form :model=\"typeForm\" label-width=\"80px\">\n                <el-form-item label=\"子类别\">\n                    <el-select v-model=\"typeForm.typeId\" placeholder=\"请选择子类别\">\n                        <el-option\n                            v-for=\"item in typeOptions\"\n                            :key=\"item.id\"\n                            :label=\"item.name\"\n                            :value=\"item.id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n            </el-form>\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"typeDialogVisible = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"updateUserType\">确 定</el-button>\n            </span>\n        </el-dialog>\n\n        <!-- 地址选择对话框 -->\n        <el-dialog title=\"设置地址信息\" :visible.sync=\"addressDialogVisible\" width=\"50%\">\n            <el-form :model=\"addressForm\" label-width=\"100px\">\n                <el-form-item label=\"省市区\">\n                    <el-cascader\n                        v-model=\"addressForm.region\"\n                        :options=\"regionOptions\"\n                        placeholder=\"请选择省/市/区\"\n                        style=\"width: 100%\"\n                    ></el-cascader>\n                </el-form-item>\n                <el-form-item label=\"详细地址\">\n                    <el-input\n                        v-model=\"addressForm.address\"\n                        type=\"textarea\"\n                        placeholder=\"请输入详细地址信息，如街道、门牌号等\"\n                        :rows=\"3\"\n                    ></el-input>\n                </el-form-item>\n            </el-form>\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addressDialogVisible = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"updateUserAddress\">确 定</el-button>\n            </span>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\n    import {mapState} from 'vuex'\n    import regionData from '@/assets/data/region.js'\n\n    export default {\n        name: \"MyProfile\",\n        data() {\n            return {\n                activeNames: ['1', '2', '3', '4', '5', '6', '7', '8'],\n                // 子类别相关\n                typeDialogVisible: false,\n                typeForm: {\n                    typeId: null\n                },\n                typeOptions: [],\n\n                // 地址相关\n                addressDialogVisible: false,\n                addressForm: {\n                    region: [],\n                    address: ''\n                },\n                regionOptions: regionData\n            }\n        },\n        computed:{\n          ...mapState('user',['user'])\n        },\n        methods: {\n            // 显示子类别选择器\n            showTypeSelector() {\n                // 获取可用的子类别列表\n                this.$get(\"/class/list\", { roleId: this.user.role.id, deptId: this.user.dept.id })\n                .then(res => {\n                    if (res.data.status && res.data.class) {\n                        this.typeOptions = res.data.class;\n                        console.log('可用的子类别列表:', this.typeOptions);\n                        this.typeDialogVisible = true;\n                    } else {\n                        this.$msg(\"获取子类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取子类别列表失败:', err);\n                    this.$msg(\"获取子类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 更新用户子类别\n            updateUserType() {\n                if (!this.typeForm.typeId) {\n                    this.$msg(\"请选择子类别\", \"warning\");\n                    return;\n                }\n\n                // 更新用户信息\n                this.$put(\"/user\", {\n                    id: this.user.id,\n                    classId: this.typeForm.typeId\n                })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$msg(\"子类别设置成功\", \"success\");\n                        this.typeDialogVisible = false;\n\n                        // 更新本地用户信息\n                        if (res.data.user) {\n                            console.log('更新后的用户数据:', res.data.user);\n                            this.$store.commit('user/setUser', res.data.user);\n                        } else {\n                            // 如果返回的数据中没有完整的用户信息，重新获取\n                            this.$get(\"/user/\" + this.user.id)\n                            .then(rs => {\n                                if (rs.data.user) {\n                                    console.log('重新获取的用户数据:', rs.data.user);\n                                    this.$store.commit('user/setUser', rs.data.user);\n                                }\n                            });\n                        }\n                    } else {\n                        this.$msg(res.data.msg || \"设置失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('设置子类别失败:', err);\n                    this.$msg(\"设置失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 显示地址选择器\n            showAddressSelector() {\n                // 如果用户已有地址信息，则预填充表单\n                if (this.user.province && this.user.city && this.user.district) {\n                    this.addressForm.region = [this.user.province, this.user.city, this.user.district];\n                }\n                if (this.user.address) {\n                    this.addressForm.address = this.user.address;\n                }\n\n                this.addressDialogVisible = true;\n            },\n\n            // 更新用户地址信息\n            updateUserAddress() {\n                if (!this.addressForm.region || this.addressForm.region.length < 3) {\n                    this.$msg(\"请选择完整的省市区信息\", \"warning\");\n                    return;\n                }\n\n                const [province, city, district] = this.addressForm.region;\n\n                // 更新用户信息\n                this.$put(\"/user\", {\n                    id: this.user.id,\n                    province,\n                    city,\n                    district,\n                    address: this.addressForm.address\n                })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$msg(\"地址信息设置成功\", \"success\");\n                        this.addressDialogVisible = false;\n\n                        // 更新本地用户信息\n                        if (res.data.user) {\n                            console.log('更新后的用户数据:', res.data.user);\n                            this.$store.commit('user/setUser', res.data.user);\n                        } else {\n                            // 如果返回的数据中没有完整的用户信息，重新获取\n                            this.$get(\"/user/\" + this.user.id)\n                            .then(rs => {\n                                if (rs.data.user) {\n                                    console.log('重新获取的用户数据:', rs.data.user);\n                                    this.$store.commit('user/setUser', rs.data.user);\n                                }\n                            });\n                        }\n                    } else {\n                        this.$msg(res.data.msg || \"设置失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('设置地址信息失败:', err);\n                    this.$msg(\"设置失败，请稍后重试\", \"error\");\n                });\n            }\n        },\n        created() {\n            console.log('MyProfile created, user data:', this.user)\n            // 确保用户数据已加载\n            if (!this.user || !this.user.role) {\n                console.warn('User data or role is missing')\n            }\n\n            // 打印用户类型信息，用于调试\n            if (this.user && this.user.role) {\n                console.log('User role ID:', this.user.role.id)\n                console.log('User dept:', this.user.dept)\n                console.log('User type:', this.user.type)\n\n                // 如果是维修员但没有type信息，尝试重新获取用户数据\n                if (this.user.role.id === 13 && !this.user.type) {\n                    this.refreshUserData();\n                }\n            }\n\n            // 添加页面可见性监听器，当页面重新获得焦点时刷新用户数据\n            this.addVisibilityListener();\n        },\n        beforeDestroy() {\n            // 移除页面可见性监听器\n            this.removeVisibilityListener();\n        },\n        methods: {\n            // 刷新用户数据\n            refreshUserData() {\n                this.$get(\"/user/\" + this.user.id)\n                .then((rs) => {\n                    if (rs.data.user) {\n                        console.log('Refreshed user data:', JSON.stringify(rs.data.user, null, 2))\n                        console.log('User type details:', rs.data.user.type ? JSON.stringify(rs.data.user.type, null, 2) : 'No type data')\n                        // 更新用户信息\n                        this.$store.commit('user/setUser', rs.data.user)\n                    }\n                })\n                .catch(err => {\n                    console.error('刷新用户数据失败:', err);\n                });\n            },\n\n            // 添加页面可见性监听器\n            addVisibilityListener() {\n                this.handleVisibilityChange = () => {\n                    if (!document.hidden) {\n                        // 页面重新获得焦点时，刷新用户数据\n                        console.log('页面重新获得焦点，刷新用户数据');\n                        this.refreshUserData();\n                    }\n                };\n                document.addEventListener('visibilitychange', this.handleVisibilityChange);\n            },\n\n            // 移除页面可见性监听器\n            removeVisibilityListener() {\n                if (this.handleVisibilityChange) {\n                    document.removeEventListener('visibilitychange', this.handleVisibilityChange);\n                }\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    /deep/ .box-card {\n        width: 60%;\n        margin: 0 auto;\n    }\n\n    /deep/ .el-card__body {\n        padding: 0 20px !important;\n    }\n\n    /deep/ .el-collapse {\n        border-top: none !important;\n    }\n\n    /deep/ .el-collapse-item__content {\n        padding-bottom: 15px !important;\n    }\n</style>\n"]}]}