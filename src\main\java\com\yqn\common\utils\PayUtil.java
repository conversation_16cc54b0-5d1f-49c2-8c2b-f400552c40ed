package com.yqn.common.utils;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alibaba.fastjson.JSONObject;
import com.yqn.pojo.RechargeRecord;
import com.yqn.service.RechargeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class PayUtil {

    @Autowired
    private RechargeRecordService rechargeRecordService;

    //appid
    private final String APP_ID = "9021000138656770";
    //应用私钥
    private final String APP_PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCPPwQjYFxAQDQgwIZdVLlEmq62TtLHPeohLJx8aDVQMqp8FkS433eCenoWmj8mVGl945OBuJdLUs74EUJq/DlQvx1G7MgH9hA9YneGvVkSShtXwVGEhzAgnovGnIIphisTA61zWa/Gxj8Dg4ZDWl/OSUGkpbl5m3u6JjIdbbV16z/UioGbXIYci8chQ3neukYTeCyDdFkuJ9QsNFfW7csJY+f9JaRyIs564cSVeVHxh0ObC/iDyhYdBWT0++TqUajpghV+xSEXTyiM7J1KA8DOxiRHD9TeNuSdnTyPdrkXYoPbax2PF9g2XZWTJ2JuNrPw5KYgzrtCvBWLJ02qLnVtAgMBAAECggEAV1ibk168ockEgDwlMl/pILYZdr0VtbxBOb1UJl/8skxSvJtviDNrUjqQm5uuihbKxKKyXVVLm/iAj68misoFwQDoP0k+p+wY9vVF4PkwiQB7L8WHiYJNjHZg0Ni6XcucFFoXt9CmaFSWM/RNOGCxLr5ospcDQ+3VQirsQBmXnrDMA8ttrEcCsiqA3/Kc2eGQpss1iK1GnY3wiV8P/j/GRZ+7deZVtYsnoF345IRHR/l8rDlZVPGY+H9fUtcv36ECwa0XANbAjOE1uIYYvel+rQtmsRYhbQZAzfmvrSptx+OmadAH/jDPF3033/GXth176Kk+HLATLKzDQv4YnvLrWQKBgQDNCKHv/tyN/ZeZXFAAuBKgYntPcUcvN+4iAh3J7maAD3/JI5e5sJ8f/zi7Oogv0csH5ATtoxxGnaDpfG1MfP8AioeF6r8hcHRLUlcHZZ0KBeJJ9N+d51CyIz9xIAsQ8k9QL4P1KARPo0zmPBb9m0mCOy+MZF/9CTE5/9kOSKbjMwKBgQCy2oSWBUYAL18QZqTaRuM7tO9OYfCXrhBMm1Mb1Uk9fSBK1hv0i5HRl2n+Gl+3aOaSxoe0hZDN6Ibo5fg9cU1C5OsNxAg+o4Z6t79iLqVwSeh6zZ2TfI7Oc7AnTkzmy1eapXfpNirHhM2hTaaHcb0STmVACDcjnZFDmy8d0bdE3wKBgQDMjuVJlQOk4jIivrfOz9c9jqOqEcFMeceqxGPF+ITpFQ2PtmQEzhMkEDUgjvgahXvDppnIISjvxwooiIxfeIHNn2gwkpEYIqLUFxVe+uEfg9lNofKMDqEy6Pm9upNv1+CLhqxnpgIPFkR9m8vwP8KqHLSGpAokYMxtKjX7Cz79gwKBgQCCJqgzL5U4D0Djg35VZR541XsLlBPrnPw5cxJwf64zc7mrG9HuDB7J4aB9DB45LroTdytLv6KXZK3AD5GszKWHl2RBeTTyuDblWI+J2B4F75Kgwr33lL2rGN7IS5+El9ahL+ZprERHOqoUxpf3mloXVzsw9ImDfmc2RlhsH695CwKBgAehyeBpOoiHTGQFWQ3vKOD0CQImJskKJ7Jdb1rU8iyC9UBNyWwAqXb6IrBeBEFw5KmMXvbAcO6M/5QluPj5GeMX0vSnwwjyGfcnOD/FPAewscmPl8mViVvO2KRyjflqr12W5JniNrOPN5gsMkJFRuyIfXlI/QiFw/Sk5AAGSj8U";
    private final String CHARSET = "UTF-8";
    // 支付宝公钥
    private final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1/jtWa17x+n9cF/tocKjEdknLeWq36NM9JC+OMfnY1tvQ+/dZnaqf0GBug9rPiabnQmXUZvSLMedantuZ65BoSwZciIPJ7bxrCnUjVCEsnweD5DQ1lGFx68nJq9dum2DwWP2gxEPk59xlqMuA7w+Voo5wzYChV3WwwfVO2TZ2e/WEM8WPa4Wjm+rm1pVLNMrWX2NpYpvyKCm4bcqOEOD14uxUzh1TUAENkKKp/v7cC/xX7SHR4qaJpkGDgXXs6CZEwgfIO1NOfO923eUINg1FqqXzbmOlhVmkiLJjjML6nyTCTk6Krk/LLSCArKm/MxjRS5Unf1CoJp8QhyAd/AfgwIDAQAB";
    //这是沙箱接口路径,正式路径为https://openapi.alipay.com/gateway.do
    private final String GATEWAY_URL = "https://openapi-sandbox.dl.alipaydev.com/gateway.do";
    private final String FORMAT = "JSON";
    //签名方式
    private final String SIGN_TYPE = "RSA2";
    //支付宝异步通知路径,付款完毕后会异步调用本项目的方法,必须为公网地址
    private final String NOTIFY_URL = " http://zb8e8674.natappfree.cc/api/alipay/toSuccess";
    //支付宝同步通知路径,也就是当付款完毕后跳转本项目的页面,可以不是公网地址
    private final String RETURN_URL = "http://localhost:8848/#/home";
    private AlipayClient alipayClient = null;
    //支付宝官方提供的接口
    public String sendRequestToAlipay(String outTradeNo, Float totalAmount, String subject) throws AlipayApiException {
        //获得初始化的AlipayClient
        alipayClient = new DefaultAlipayClient(GATEWAY_URL, APP_ID, APP_PRIVATE_KEY, FORMAT, CHARSET, ALIPAY_PUBLIC_KEY, SIGN_TYPE);

        //设置请求参数
        AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
        alipayRequest.setReturnUrl(RETURN_URL);
        alipayRequest.setNotifyUrl(NOTIFY_URL);

        //商品描述（可空）
        String body = "";
        alipayRequest.setBizContent("{\"out_trade_no\":\"" + outTradeNo + "\","
                + "\"total_amount\":\"" + totalAmount + "\","
                + "\"subject\":\"" + subject + "\","
                + "\"body\":\"" + body + "\","
                + "\"product_code\":\"FAST_INSTANT_TRADE_PAY\"}");

        //请求
        String result = alipayClient.pageExecute(alipayRequest).getBody();
        System.out.println("返回的结果是："+result );
        return result;
    }

    //    通过订单编号查询
    public String query(String id){
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", id);
        request.setBizContent(bizContent.toString());
        AlipayTradeQueryResponse response = null;
        String body=null;
        try {
            response = alipayClient.execute(request);
            body = response.getBody();
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        if(response.isSuccess()){
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
        return body;
    }

    /**
     * 验证支付宝签名
     * @param request HTTP请求
     * @return 是否验证成功
     */
    public boolean verifySignature(HttpServletRequest request) {
        try {
            // 获取支付宝POST过来反馈信息
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();

            for (String name : requestParams.keySet()) {
                String[] values = requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(name, valueStr);
            }

            // 调用SDK验证签名
            return AlipaySignature.rsaCheckV1(params, ALIPAY_PUBLIC_KEY, CHARSET, SIGN_TYPE);

        } catch (AlipayApiException e) {
            log.error("支付宝签名验证异常", e);
            return false;
        }
    }

    /**
     * 获取支付宝回调参数
     * @param request HTTP请求
     * @return 参数Map
     */
    public Map<String, String> getCallbackParams(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();

        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }

        return params;
    }

    /**
     * 创建充值订单号
     * @param userId 用户ID
     * @return 订单号
     */
    public String generateOrderNo(Long userId) {
        return "RECHARGE_" + userId + "_" + System.currentTimeMillis();
    }

    /**
     * 创建充值支付订单
     * @param userId 用户ID
     * @param amount 充值金额
     * @param subject 订单标题
     * @return 支付表单HTML
     */
    public String createRechargeOrder(Long userId, Double amount, String subject) throws AlipayApiException {
        // 生成订单号
        String orderNo = generateOrderNo(userId);

        // 创建充值记录
        RechargeRecord record = new RechargeRecord();
        record.setUserId(userId);
        record.setOrderNo(orderNo);
        record.setAmount(amount);
        record.setStatus(0); // 待支付
        record.setPaymentMethod("alipay");
        record.setCreateTime(new Date());
        record.setRemark("支付宝充值");

        // 保存充值记录
        rechargeRecordService.save(record);

        // 调用支付宝接口
        return sendRequestToAlipay(orderNo, amount.floatValue(), subject);
    }

    /**
     * 处理支付宝异步通知
     * @param request HTTP请求
     * @return 处理结果
     */
    public String handleAlipayNotify(HttpServletRequest request) {
        try {
            // 验证签名
            if (!verifySignature(request)) {
                log.error("支付宝异步通知签名验证失败");
                return "fail";
            }

            // 获取回调参数
            Map<String, String> params = getCallbackParams(request);

            String outTradeNo = params.get("out_trade_no"); // 商户订单号
            String tradeNo = params.get("trade_no"); // 支付宝交易号
            String tradeStatus = params.get("trade_status"); // 交易状态
            String totalAmount = params.get("total_amount"); // 交易金额

            log.info("支付宝异步通知：订单号={}, 交易号={}, 状态={}, 金额={}",
                    outTradeNo, tradeNo, tradeStatus, totalAmount);

            // 查询充值记录
            RechargeRecord record = rechargeRecordService.getByOrderNo(outTradeNo);
            if (record == null) {
                log.error("充值记录不存在，订单号：{}", outTradeNo);
                return "fail";
            }

            // 验证金额
            if (!record.getAmount().equals(Double.valueOf(totalAmount))) {
                log.error("金额不匹配，订单号：{}，记录金额：{}，通知金额：{}",
                        outTradeNo, record.getAmount(), totalAmount);
                return "fail";
            }

            // 处理支付成功
            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                if (record.getStatus() == 0) { // 只处理待支付状态的订单
                    // 更新充值记录状态
                    rechargeRecordService.updatePaymentStatus(outTradeNo, 1, tradeNo);

                    log.info("充值成功，用户ID：{}，金额：{}", record.getUserId(), record.getAmount());
                }
            }

            return "success";

        } catch (Exception e) {
            log.error("处理支付宝异步通知异常", e);
            return "fail";
        }
    }
}
