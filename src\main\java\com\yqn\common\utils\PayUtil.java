package com.yqn.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PayUtil {
    @Autowired
 
    //appid
    private final String APP_ID = "9021000138656770";
    //应用私钥
    private final String APP_PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCPPwQjYFxAQDQgwIZdVLlEmq62TtLHPeohLJx8aDVQMqp8FkS433eCenoWmj8mVGl945OBuJdLUs74EUJq/DlQvx1G7MgH9hA9YneGvVkSShtXwVGEhzAgnovGnIIphisTA61zWa/Gxj8Dg4ZDWl/OSUGkpbl5m3u6JjIdbbV16z/UioGbXIYci8chQ3neukYTeCyDdFkuJ9QsNFfW7csJY+f9JaRyIs564cSVeVHxh0ObC/iDyhYdBWT0++TqUajpghV+xSEXTyiM7J1KA8DOxiRHD9TeNuSdnTyPdrkXYoPbax2PF9g2XZWTJ2JuNrPw5KYgzrtCvBWLJ02qLnVtAgMBAAECggEAV1ibk168ockEgDwlMl/pILYZdr0VtbxBOb1UJl/8skxSvJtviDNrUjqQm5uuihbKxKKyXVVLm/iAj68misoFwQDoP0k+p+wY9vVF4PkwiQB7L8WHiYJNjHZg0Ni6XcucFFoXt9CmaFSWM/RNOGCxLr5ospcDQ+3VQirsQBmXnrDMA8ttrEcCsiqA3/Kc2eGQpss1iK1GnY3wiV8P/j/GRZ+7deZVtYsnoF345IRHR/l8rDlZVPGY+H9fUtcv36ECwa0XANbAjOE1uIYYvel+rQtmsRYhbQZAzfmvrSptx+OmadAH/jDPF3033/GXth176Kk+HLATLKzDQv4YnvLrWQKBgQDNCKHv/tyN/ZeZXFAAuBKgYntPcUcvN+4iAh3J7maAD3/JI5e5sJ8f/zi7Oogv0csH5ATtoxxGnaDpfG1MfP8AioeF6r8hcHRLUlcHZZ0KBeJJ9N+d51CyIz9xIAsQ8k9QL4P1KARPo0zmPBb9m0mCOy+MZF/9CTE5/9kOSKbjMwKBgQCy2oSWBUYAL18QZqTaRuM7tO9OYfCXrhBMm1Mb1Uk9fSBK1hv0i5HRl2n+Gl+3aOaSxoe0hZDN6Ibo5fg9cU1C5OsNxAg+o4Z6t79iLqVwSeh6zZ2TfI7Oc7AnTkzmy1eapXfpNirHhM2hTaaHcb0STmVACDcjnZFDmy8d0bdE3wKBgQDMjuVJlQOk4jIivrfOz9c9jqOqEcFMeceqxGPF+ITpFQ2PtmQEzhMkEDUgjvgahXvDppnIISjvxwooiIxfeIHNn2gwkpEYIqLUFxVe+uEfg9lNofKMDqEy6Pm9upNv1+CLhqxnpgIPFkR9m8vwP8KqHLSGpAokYMxtKjX7Cz79gwKBgQCCJqgzL5U4D0Djg35VZR541XsLlBPrnPw5cxJwf64zc7mrG9HuDB7J4aB9DB45LroTdytLv6KXZK3AD5GszKWHl2RBeTTyuDblWI+J2B4F75Kgwr33lL2rGN7IS5+El9ahL+ZprERHOqoUxpf3mloXVzsw9ImDfmc2RlhsH695CwKBgAehyeBpOoiHTGQFWQ3vKOD0CQImJskKJ7Jdb1rU8iyC9UBNyWwAqXb6IrBeBEFw5KmMXvbAcO6M/5QluPj5GeMX0vSnwwjyGfcnOD/FPAewscmPl8mViVvO2KRyjflqr12W5JniNrOPN5gsMkJFRuyIfXlI/QiFw/Sk5AAGSj8U";
    private final String CHARSET = "UTF-8";
    // 支付宝公钥
    private final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1/jtWa17x+n9cF/tocKjEdknLeWq36NM9JC+OMfnY1tvQ+/dZnaqf0GBug9rPiabnQmXUZvSLMedantuZ65BoSwZciIPJ7bxrCnUjVCEsnweD5DQ1lGFx68nJq9dum2DwWP2gxEPk59xlqMuA7w+Voo5wzYChV3WwwfVO2TZ2e/WEM8WPa4Wjm+rm1pVLNMrWX2NpYpvyKCm4bcqOEOD14uxUzh1TUAENkKKp/v7cC/xX7SHR4qaJpkGDgXXs6CZEwgfIO1NOfO923eUINg1FqqXzbmOlhVmkiLJjjML6nyTCTk6Krk/LLSCArKm/MxjRS5Unf1CoJp8QhyAd/AfgwIDAQAB";
    //这是沙箱接口路径,正式路径为https://openapi.alipay.com/gateway.do
    private final String GATEWAY_URL = "https://openapi-sandbox.dl.alipaydev.com/gateway.do";
    private final String FORMAT = "JSON";
    //签名方式
    private final String SIGN_TYPE = "RSA2";
    //支付宝异步通知路径,付款完毕后会异步调用本项目的方法,必须为公网地址
    private final String NOTIFY_URL = " http://zb8e8674.natappfree.cc/api/alipay/toSuccess";
    //支付宝同步通知路径,也就是当付款完毕后跳转本项目的页面,可以不是公网地址
    private final String RETURN_URL = "http://localhost:8080/api/alipay/toSuccess";
    private AlipayClient alipayClient = null;
    //支付宝官方提供的接口
    public String sendRequestToAlipay(String outTradeNo, Float totalAmount, String subject) throws AlipayApiException {
        //获得初始化的AlipayClient
        alipayClient = new DefaultAlipayClient(GATEWAY_URL, APP_ID, APP_PRIVATE_KEY, FORMAT, CHARSET, ALIPAY_PUBLIC_KEY, SIGN_TYPE);

        //设置请求参数
        AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
        alipayRequest.setReturnUrl(RETURN_URL);
        alipayRequest.setNotifyUrl(NOTIFY_URL);

        //商品描述（可空）
        String body = "";
        alipayRequest.setBizContent("{\"out_trade_nos\":\"" + outTradeNo + "\","
                + "\"total_amount\":\"" + totalAmount + "\","
                + "\"subject\":\"" + subject + "\","
                + "\"body\":\"" + body + "\","
                + "\"product_code\":\"FAST_INSTANT_TRADE_PAY\"}");

        //请求
        String result = alipayClient.pageExecute(alipayRequest).getBody();
        System.out.println("返回的结果是："+result );
        return result;
    }

    //    通过订单编号查询
    public String query(String id){
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", id);
        request.setBizContent(bizContent.toString());
        AlipayTradeQueryResponse response = null;
        String body=null;
        try {
            response = alipayClient.execute(request);
            body = response.getBody();
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        if(response.isSuccess()){
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
        return body;
    }
}
