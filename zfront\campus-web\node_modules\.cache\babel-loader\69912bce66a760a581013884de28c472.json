{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\MyProfile.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\MyProfile.vue", "mtime": 1748711742663}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MyProfile.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8GA,SAAA,QAAA,QAAA,MAAA;AACA,OAAA,UAAA,MAAA,yBAAA;AAEA;AACA,EAAA,IAAA,EAAA,WADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,CAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,CADA;AAEA;AACA,MAAA,iBAAA,EAAA,KAHA;AAIA,MAAA,QAAA,EAAA;AACA,QAAA,MAAA,EAAA;AADA,OAJA;AAOA,MAAA,WAAA,EAAA,EAPA;AASA;AACA,MAAA,oBAAA,EAAA,KAVA;AAWA,MAAA,WAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA;AAFA,OAXA;AAeA,MAAA,aAAA,EAAA;AAfA,KAAA;AAiBA,GApBA;AAqBA,EAAA,QAAA,oBACA,QAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,CADA,CArBA;AAwBA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,gBAFA,8BAEA;AAAA;;AACA;AACA,WAAA,IAAA,CAAA,aAAA,EAAA;AAAA,QAAA,MAAA,EAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AAAA,QAAA,MAAA,EAAA,KAAA,IAAA,CAAA,IAAA,CAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACA,UAAA,KAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,KAAA,CAAA,WAAA;AACA,UAAA,KAAA,CAAA,iBAAA,GAAA,IAAA;AACA,SAJA,MAIA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,WAAA,EAAA,OAAA;AACA;AACA,OATA,EAUA,KAVA,CAUA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,YAAA,EAAA,GAAA;;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,iBAAA,EAAA,OAAA;AACA,OAbA;AAcA,KAlBA;AAoBA;AACA,IAAA,cArBA,4BAqBA;AAAA;;AACA,UAAA,CAAA,KAAA,QAAA,CAAA,MAAA,EAAA;AACA,aAAA,IAAA,CAAA,QAAA,EAAA,SAAA;AACA;AACA,OAJA,CAMA;;;AACA,WAAA,IAAA,CAAA,OAAA,EAAA;AACA,QAAA,EAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,QAAA,OAAA,EAAA,KAAA,QAAA,CAAA;AAFA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,SAAA;;AACA,UAAA,MAAA,CAAA,iBAAA,GAAA,KAAA,CAFA,CAIA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,cAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,WAHA,MAGA;AACA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,WAAA,MAAA,CAAA,IAAA,CAAA,EAAA,EACA,IADA,CACA,UAAA,EAAA,EAAA;AACA,kBAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,cAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,aANA;AAOA;AACA,SAlBA,MAkBA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA,EAAA,OAAA;AACA;AACA,OA1BA,EA2BA,KA3BA,CA2BA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,UAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,YAAA,EAAA,OAAA;AACA,OA9BA;AA+BA,KA3DA;AA6DA;AACA,IAAA,mBA9DA,iCA8DA;AACA;AACA,UAAA,KAAA,IAAA,CAAA,QAAA,IAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,CAAA,KAAA,IAAA,CAAA,QAAA,EAAA,KAAA,IAAA,CAAA,IAAA,EAAA,KAAA,IAAA,CAAA,QAAA,CAAA;AACA;;AACA,UAAA,KAAA,IAAA,CAAA,OAAA,EAAA;AACA,aAAA,WAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA;;AAEA,WAAA,oBAAA,GAAA,IAAA;AACA,KAxEA;AA0EA;AACA,IAAA,iBA3EA,+BA2EA;AAAA;;AACA,UAAA,CAAA,KAAA,WAAA,CAAA,MAAA,IAAA,KAAA,WAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,IAAA,CAAA,aAAA,EAAA,SAAA;AACA;AACA;;AAEA,iDAAA,KAAA,WAAA,CAAA,MAAA;AAAA,UAAA,QAAA;AAAA,UAAA,IAAA;AAAA,UAAA,QAAA,4BANA,CAQA;;;AACA,WAAA,IAAA,CAAA,OAAA,EAAA;AACA,QAAA,EAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,QAAA,QAAA,EAAA,QAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,QAAA,EAAA,QAJA;AAKA,QAAA,OAAA,EAAA,KAAA,WAAA,CAAA;AALA,OAAA,EAOA,IAPA,CAOA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,SAAA;;AACA,UAAA,MAAA,CAAA,oBAAA,GAAA,KAAA,CAFA,CAIA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,cAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,WAHA,MAGA;AACA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,WAAA,MAAA,CAAA,IAAA,CAAA,EAAA,EACA,IADA,CACA,UAAA,EAAA,EAAA;AACA,kBAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,cAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,aANA;AAOA;AACA,SAlBA,MAkBA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA,EAAA,OAAA;AACA;AACA,OA7BA,EA8BA,KA9BA,CA8BA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAjCA;AAkCA;AAtHA,GAxBA;AAgJA,EAAA,OAhJA,qBAgJA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,KAAA,IAAA,EADA,CAEA;;AACA,QAAA,CAAA,KAAA,IAAA,IAAA,CAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,MAAA,OAAA,CAAA,IAAA,CAAA,8BAAA;AACA,KALA,CAOA;;;AACA,QAAA,KAAA,IAAA,IAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,IAAA,CAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,IAAA,CAAA,IAAA,EAHA,CAKA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,IAAA,CAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,aAAA,eAAA;AACA;AACA,KAjBA,CAmBA;;;AACA,SAAA,qBAAA;AACA,GArKA;AAsKA,EAAA,aAtKA,2BAsKA;AACA;AACA,SAAA,wBAAA;AACA;AAzKA,cA0KA;AACA;AACA,EAAA,eAFA,6BAEA;AAAA;;AACA,SAAA,IAAA,CAAA,WAAA,KAAA,IAAA,CAAA,EAAA,EACA,IADA,CACA,UAAA,EAAA,EAAA;AACA,UAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,sBAAA,EAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,oBAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA,GAAA,cAAA,EAFA,CAGA;;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,cAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,KARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;AACA,KAXA;AAYA,GAfA;AAiBA;AACA,EAAA,qBAlBA,mCAkBA;AAAA;;AACA,SAAA,sBAAA,GAAA,YAAA;AACA,UAAA,CAAA,QAAA,CAAA,MAAA,EAAA;AACA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,iBAAA;;AACA,QAAA,MAAA,CAAA,eAAA;AACA;AACA,KANA;;AAOA,IAAA,QAAA,CAAA,gBAAA,CAAA,kBAAA,EAAA,KAAA,sBAAA;AACA,GA3BA;AA6BA;AACA,EAAA,wBA9BA,sCA8BA;AACA,QAAA,KAAA,sBAAA,EAAA;AACA,MAAA,QAAA,CAAA,mBAAA,CAAA,kBAAA,EAAA,KAAA,sBAAA;AACA;AACA;AAlCA,CA1KA", "sourcesContent": ["<template>\n    <div>\n        <el-card class=\"box-card\">\n            <div slot=\"header\" class=\"clearfix\">\n                <span>基本信息</span>\n                <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"$emit('personalInformation')\">修改信息</el-button>\n            </div>\n            <div class=\"content_txt\">\n                <el-collapse v-model=\"activeNames\">\n                    <el-collapse-item title=\"账号\" name=\"1\">\n                        <div>{{user.studentId}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"姓名\" name=\"2\">\n                        <div>{{user.username}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"手机号\" name=\"3\">\n                        <div>{{user.phone}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"角色\" name=\"4\">\n                        <div>{{user.role && user.role.name}}</div>\n                    </el-collapse-item>\n                    <!-- 用户角色的特定信息 -->\n                    <template v-if=\"user.role && user.role.id === 14\">\n                        <!-- 用户角色不需要显示类别信息 -->\n                    </template>\n                    <!-- 维修员角色的特定信息 -->\n                    <template v-else-if=\"user.role && user.role.id === 13\">\n                        <el-collapse-item v-if=\"user.dept\" title=\"类别\" name=\"5\">\n                            <div>{{user.dept.name}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item v-if=\"user.type\" title=\"子类别\" name=\"6\">\n                            <div>{{user.type.name}}</div>\n                        </el-collapse-item>\n                    </template>\n                    <!-- 其他角色的特定信息 -->\n                    <!-- <template v-else>\n                        <el-collapse-item v-if=\"user.dept\" title=\"部门\" name=\"5\">\n                            <div>{{user.dept.name}}</div>\n                        </el-collapse-item>\n                    </template> -->\n                    <!-- 维修员地址信息 -->\n                    <template v-if=\"user.role && user.role.id === 13\">\n                        <el-collapse-item title=\"地址信息\" name=\"7\">\n                            <div v-if=\"user.province && user.city && user.district\">\n                                <p>{{user.province}} {{user.city}} {{user.district}}</p>\n                                <!-- <p v-if=\"user.address\">详细地址: {{user.address}}</p> -->\n                            </div>\n                            <div v-else>\n\n                                <el-button type=\"text\" @click=\"showAddressSelector\">设置地址</el-button>\n                            </div>\n                        </el-collapse-item>\n                    </template>\n\n                    <el-collapse-item title=\"余额\" name=\"8\">\n                        <div><i class=\"el-icon-money\" style=\"color: red;\"> {{user.balance}}元</i></div>\n                    </el-collapse-item>\n                </el-collapse>\n            </div>\n        </el-card>\n\n        <!-- 子类别选择对话框 -->\n        <el-dialog title=\"选择子类别\" :visible.sync=\"typeDialogVisible\" width=\"30%\">\n            <el-form :model=\"typeForm\" label-width=\"80px\">\n                <el-form-item label=\"子类别\">\n                    <el-select v-model=\"typeForm.typeId\" placeholder=\"请选择子类别\">\n                        <el-option\n                            v-for=\"item in typeOptions\"\n                            :key=\"item.id\"\n                            :label=\"item.name\"\n                            :value=\"item.id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n            </el-form>\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"typeDialogVisible = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"updateUserType\">确 定</el-button>\n            </span>\n        </el-dialog>\n\n        <!-- 地址选择对话框 -->\n        <el-dialog title=\"设置地址信息\" :visible.sync=\"addressDialogVisible\" width=\"50%\">\n            <el-form :model=\"addressForm\" label-width=\"100px\">\n                <el-form-item label=\"省市区\">\n                    <el-cascader\n                        v-model=\"addressForm.region\"\n                        :options=\"regionOptions\"\n                        placeholder=\"请选择省/市/区\"\n                        style=\"width: 100%\"\n                    ></el-cascader>\n                </el-form-item>\n                <el-form-item label=\"详细地址\">\n                    <el-input\n                        v-model=\"addressForm.address\"\n                        type=\"textarea\"\n                        placeholder=\"请输入详细地址信息，如街道、门牌号等\"\n                        :rows=\"3\"\n                    ></el-input>\n                </el-form-item>\n            </el-form>\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"addressDialogVisible = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"updateUserAddress\">确 定</el-button>\n            </span>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\n    import {mapState} from 'vuex'\n    import regionData from '@/assets/data/region.js'\n\n    export default {\n        name: \"MyProfile\",\n        data() {\n            return {\n                activeNames: ['1', '2', '3', '4', '5', '6', '7', '8'],\n                // 子类别相关\n                typeDialogVisible: false,\n                typeForm: {\n                    typeId: null\n                },\n                typeOptions: [],\n\n                // 地址相关\n                addressDialogVisible: false,\n                addressForm: {\n                    region: [],\n                    address: ''\n                },\n                regionOptions: regionData\n            }\n        },\n        computed:{\n          ...mapState('user',['user'])\n        },\n        methods: {\n            // 显示子类别选择器\n            showTypeSelector() {\n                // 获取可用的子类别列表\n                this.$get(\"/class/list\", { roleId: this.user.role.id, deptId: this.user.dept.id })\n                .then(res => {\n                    if (res.data.status && res.data.class) {\n                        this.typeOptions = res.data.class;\n                        console.log('可用的子类别列表:', this.typeOptions);\n                        this.typeDialogVisible = true;\n                    } else {\n                        this.$msg(\"获取子类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取子类别列表失败:', err);\n                    this.$msg(\"获取子类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 更新用户子类别\n            updateUserType() {\n                if (!this.typeForm.typeId) {\n                    this.$msg(\"请选择子类别\", \"warning\");\n                    return;\n                }\n\n                // 更新用户信息\n                this.$put(\"/user\", {\n                    id: this.user.id,\n                    classId: this.typeForm.typeId\n                })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$msg(\"子类别设置成功\", \"success\");\n                        this.typeDialogVisible = false;\n\n                        // 更新本地用户信息\n                        if (res.data.user) {\n                            console.log('更新后的用户数据:', res.data.user);\n                            this.$store.commit('user/setUser', res.data.user);\n                        } else {\n                            // 如果返回的数据中没有完整的用户信息，重新获取\n                            this.$get(\"/user/\" + this.user.id)\n                            .then(rs => {\n                                if (rs.data.user) {\n                                    console.log('重新获取的用户数据:', rs.data.user);\n                                    this.$store.commit('user/setUser', rs.data.user);\n                                }\n                            });\n                        }\n                    } else {\n                        this.$msg(res.data.msg || \"设置失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('设置子类别失败:', err);\n                    this.$msg(\"设置失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 显示地址选择器\n            showAddressSelector() {\n                // 如果用户已有地址信息，则预填充表单\n                if (this.user.province && this.user.city && this.user.district) {\n                    this.addressForm.region = [this.user.province, this.user.city, this.user.district];\n                }\n                if (this.user.address) {\n                    this.addressForm.address = this.user.address;\n                }\n\n                this.addressDialogVisible = true;\n            },\n\n            // 更新用户地址信息\n            updateUserAddress() {\n                if (!this.addressForm.region || this.addressForm.region.length < 3) {\n                    this.$msg(\"请选择完整的省市区信息\", \"warning\");\n                    return;\n                }\n\n                const [province, city, district] = this.addressForm.region;\n\n                // 更新用户信息\n                this.$put(\"/user\", {\n                    id: this.user.id,\n                    province,\n                    city,\n                    district,\n                    address: this.addressForm.address\n                })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$msg(\"地址信息设置成功\", \"success\");\n                        this.addressDialogVisible = false;\n\n                        // 更新本地用户信息\n                        if (res.data.user) {\n                            console.log('更新后的用户数据:', res.data.user);\n                            this.$store.commit('user/setUser', res.data.user);\n                        } else {\n                            // 如果返回的数据中没有完整的用户信息，重新获取\n                            this.$get(\"/user/\" + this.user.id)\n                            .then(rs => {\n                                if (rs.data.user) {\n                                    console.log('重新获取的用户数据:', rs.data.user);\n                                    this.$store.commit('user/setUser', rs.data.user);\n                                }\n                            });\n                        }\n                    } else {\n                        this.$msg(res.data.msg || \"设置失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('设置地址信息失败:', err);\n                    this.$msg(\"设置失败，请稍后重试\", \"error\");\n                });\n            }\n        },\n        created() {\n            console.log('MyProfile created, user data:', this.user)\n            // 确保用户数据已加载\n            if (!this.user || !this.user.role) {\n                console.warn('User data or role is missing')\n            }\n\n            // 打印用户类型信息，用于调试\n            if (this.user && this.user.role) {\n                console.log('User role ID:', this.user.role.id)\n                console.log('User dept:', this.user.dept)\n                console.log('User type:', this.user.type)\n\n                // 如果是维修员但没有type信息，尝试重新获取用户数据\n                if (this.user.role.id === 13 && !this.user.type) {\n                    this.refreshUserData();\n                }\n            }\n\n            // 添加页面可见性监听器，当页面重新获得焦点时刷新用户数据\n            this.addVisibilityListener();\n        },\n        beforeDestroy() {\n            // 移除页面可见性监听器\n            this.removeVisibilityListener();\n        },\n        methods: {\n            // 刷新用户数据\n            refreshUserData() {\n                this.$get(\"/user/\" + this.user.id)\n                .then((rs) => {\n                    if (rs.data.user) {\n                        console.log('Refreshed user data:', JSON.stringify(rs.data.user, null, 2))\n                        console.log('User type details:', rs.data.user.type ? JSON.stringify(rs.data.user.type, null, 2) : 'No type data')\n                        // 更新用户信息\n                        this.$store.commit('user/setUser', rs.data.user)\n                    }\n                })\n                .catch(err => {\n                    console.error('刷新用户数据失败:', err);\n                });\n            },\n\n            // 添加页面可见性监听器\n            addVisibilityListener() {\n                this.handleVisibilityChange = () => {\n                    if (!document.hidden) {\n                        // 页面重新获得焦点时，刷新用户数据\n                        console.log('页面重新获得焦点，刷新用户数据');\n                        this.refreshUserData();\n                    }\n                };\n                document.addEventListener('visibilitychange', this.handleVisibilityChange);\n            },\n\n            // 移除页面可见性监听器\n            removeVisibilityListener() {\n                if (this.handleVisibilityChange) {\n                    document.removeEventListener('visibilitychange', this.handleVisibilityChange);\n                }\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    /deep/ .box-card {\n        width: 60%;\n        margin: 0 auto;\n    }\n\n    /deep/ .el-card__body {\n        padding: 0 20px !important;\n    }\n\n    /deep/ .el-collapse {\n        border-top: none !important;\n    }\n\n    /deep/ .el-collapse-item__content {\n        padding-bottom: 15px !important;\n    }\n</style>\n"], "sourceRoot": "src/views/user/children"}]}