server:
  port: 8081

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************
    username: root
    password: zwy20030714

    druid:
      aop-patterns: com.yqn.*
      filters: stat,wall

      stat-view-servlet:
        enabled: true
        login-username: admin
        login-password: admin

      web-stat-filter:
        enabled: true
        url-pattern: /*
logging:
  level:
    com.yqn.mapper: debug
