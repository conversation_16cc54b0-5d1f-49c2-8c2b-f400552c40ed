package com.yqn.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yqn.pojo.Task;
import com.yqn.pojo.User;
import com.yqn.service.TaskService;
import com.yqn.common.tools.MessageTools;
import com.yqn.common.tools.PocketMoney;
import com.yqn.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.List;
import java.util.Map;
import com.yqn.common.annotation.RoleAccess;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/task")
@Slf4j
public class TaskController {
    @Autowired
    private TaskService taskService;
    @Autowired
    private UserService userService;
    @Autowired
    private MessageTools message;
    @Autowired
    private PocketMoney money;

    // 获取当前登录user所在学校的任务
    @GetMapping
    public Map<String, Object> tasks(Long id) {
        // 如果是管理员请求（没有传id），返回所有任务
        if (id == null) {
            QueryWrapper<Task> wrapper = new QueryWrapper<>();
            // 按创建时间降序
            wrapper.orderByDesc("create_time");
            List<Task> tasks = taskService.list(wrapper);
            return message.message(true, "请求成功", "task", tasks);
        }

        // 普通用户请求
        User user = userService.getById(id);
        if (user != null) {
            QueryWrapper<Task> wrapper = new QueryWrapper<>();
            if (user.getRole().getId() == 13) {
                // 学校ID为13的用户可以看到学校ID为14的用户发布的任务
                wrapper.eq("user_role_id", 14L)  // 查看学校ID为14的用户发布的任务
                       .eq("state", 0)  // 只查看未被接受的任务
                       .orderByDesc("create_time");  // 按创建时间倒序排序
            } else if (user.getRole().getId() == 14) {
                // 学校ID为14的用户只能看到自己发布的任务
                wrapper.eq("publish_user_id", user.getId())
                       .orderByDesc("create_time");
            }

            List<Task> tasks = taskService.list(wrapper);
            return message.message(true, "请求成功", "task", tasks);
        }
        return message.message(false, "用户不存在", "task", null);
    }

    // 根据id获取task
    @GetMapping("/{id}")
    public Map<String, Object> task(@PathVariable String id) {
        Task task = taskService.getById(id);
        return message.message(true, "请求成功", "task", task);
    }

    // 当前登录User, 已发布的task
    @GetMapping("/published")
    public Map<String, Object> published(Long id) {
        return message.message(true, "请求成功", "task", publishAndAcceptMethods(id, "publish_user_id"));
    }

    // 当前登录User, 已接受的task
    @GetMapping("/accepted")
    public Map<String, Object> accepted(Long id) {
        return message.message(true, "请求成功", "task", publishAndAcceptMethods(id, "accept_user_id"));
    }

    // 获取发布和接受的task
    public List<Task> publishAndAcceptMethods(Long id, String field) {
        // User user = (User) session.getAttribute("user");
        QueryWrapper<Task> wrapper = new QueryWrapper<>();
        wrapper.eq(field, id);
        return taskService.list(wrapper);
    }

    // 发布新task
    @PostMapping
    @RoleAccess({14})
    public Map<String, Object> saveTask(@RequestBody Task task) {
        User user = userService.getById(task.getPublishId());
        if (user == null) {
            return message.message(false, "用户不存在", "", null);
        }

        if (user.getBalance() >= task.getReward()) {
            // 设置任务的学校ID
            task.setUserRoleId(user.getRole().getId());
            task.setState(0);
            task.setCreateTime(new Date());  // 设置创建时间

            boolean save = taskService.save(task);
            if (save) {
                money.transfer("balance=balance-", task.getReward(), user.getStudentId());

                // 如果启用了自动派单，尝试自动分配维修员
                if (task.getAutoAssign() != null && task.getAutoAssign()) {
                    autoAssignTask(task);
                }

                // 获取完整的任务信息（包括关联的用户信息）
                Task savedTask = taskService.getById(task.getId());
                return message.message(true, "发布任务成功", "task", savedTask);
            }
            return message.message(false, "发布任务失败", "", null);
        } else {
            return message.message(false, "余额不足", "", null);
        }
    }

    /**
     * 自动派单功能
     * @param task 需要派单的任务
     */
    private void autoAssignTask(Task task) {
        try {
            // 确保任务有类别和子类别ID
            if (task.getTypeId() == null) {
                log.warn("自动派单失败：任务没有指定子类别");
                return;
            }

            if (task.getDeptId() == null) {
                log.warn("自动派单失败：任务没有指定类别");
                return;
            }

            // 查询符合条件的维修员
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("role_id", 13) // 维修员角色ID
                   .eq("dept_id", task.getDeptId()) // 匹配类别
                   .eq("type_id", task.getTypeId()) // 匹配子类别
                   .eq("state", 1); // 确保账号状态正常

            List<User> eligibleMaintainers = userService.list(wrapper);
            if (eligibleMaintainers.isEmpty()) {
                log.warn("自动派单失败：没有找到匹配子类别的维修员");
                return;
            }

            User selectedMaintainer = null;

            // 根据派单优先级选择维修员
            if (task.getAssignPriority() != null && task.getAssignPriority() == 1) {
                // 地址优先
                selectedMaintainer = selectMaintainerByLocation(eligibleMaintainers, task);
            } else {
                // 评分优先（默认）
                selectedMaintainer = selectMaintainerByRating(eligibleMaintainers, task);
            }

            // 如果找到合适的维修员，自动接单
            if (selectedMaintainer != null) {
                Task updateTask = new Task();
                updateTask.setId(task.getId());
                updateTask.setAcceptId(selectedMaintainer.getId());
                updateTask.setState(1);
                updateTask.setOrderTime(new Date());

                boolean success = taskService.updateById(updateTask);
                if (success) {
                    log.info("自动派单成功：任务ID={}, 维修员ID={}", task.getId(), selectedMaintainer.getId());
                } else {
                    log.warn("自动派单失败：更新任务状态失败");
                }
            } else {
                log.warn("自动派单失败：没有找到合适的维修员");
            }
        } catch (Exception e) {
            log.error("自动派单过程中发生错误", e);
        }
    }

    /**
     * 根据地理位置选择维修员
     * 优先顺序：区 > 市 > 省
     */
    private User selectMaintainerByLocation(List<User> maintainers, Task task) {
        // 按接单量排序
        maintainers.sort((m1, m2) -> {
            // 获取接单量
            int m1Orders = getAcceptedOrderCount(m1.getId());
            int m2Orders = getAcceptedOrderCount(m2.getId());
            return Integer.compare(m1Orders, m2Orders);
        });

        // 如果任务没有地址信息，直接返回接单量最少的维修员
        if (task.getDistrict() == null || task.getCity() == null || task.getProvince() == null) {
            return maintainers.isEmpty() ? null : maintainers.get(0);
        }

        // 1. 优先选择同区的维修员
        for (User maintainer : maintainers) {
            if (task.getDistrict().equals(maintainer.getDistrict())) {
                return maintainer;
            }
        }

        // 2. 其次选择同市的维修员
        for (User maintainer : maintainers) {
            if (task.getCity().equals(maintainer.getCity())) {
                return maintainer;
            }
        }

        // 3. 最后选择同省的维修员
        for (User maintainer : maintainers) {
            if (task.getProvince().equals(maintainer.getProvince())) {
                return maintainer;
            }
        }

        // 如果没有地理位置匹配的维修员，返回接单量最少的维修员
        return maintainers.isEmpty() ? null : maintainers.get(0);
    }

    /**
     * 根据评分选择维修员
     * 优先考虑市内的高分维修员，其次是接单量少的维修员
     */
    private User selectMaintainerByRating(List<User> maintainers, Task task) {
        // 先筛选出同市的维修员
        List<User> sameCityMaintainers = new java.util.ArrayList<>();
        for (User maintainer : maintainers) {
            if (task.getCity() != null && task.getCity().equals(maintainer.getCity())) {
                sameCityMaintainers.add(maintainer);
            }
        }

        // 如果同市没有维修员，使用所有维修员
        List<User> candidateMaintainers = sameCityMaintainers.isEmpty() ? maintainers : sameCityMaintainers;

        // 按评分和接单量排序
        candidateMaintainers.sort((m1, m2) -> {
            // 获取评分（这里需要实现获取维修员评分的方法）
            double m1Rating = getMaintainerRating(m1.getId());
            double m2Rating = getMaintainerRating(m2.getId());

            // 评分相同时，按接单量排序
            if (Math.abs(m1Rating - m2Rating) < 0.1) {
                int m1Orders = getAcceptedOrderCount(m1.getId());
                int m2Orders = getAcceptedOrderCount(m2.getId());
                return Integer.compare(m1Orders, m2Orders);
            }

            // 评分不同时，按评分降序排序
            return Double.compare(m2Rating, m1Rating);
        });

        return candidateMaintainers.isEmpty() ? null : candidateMaintainers.get(0);
    }

    /**
     * 获取维修员的接单数量
     */
    private int getAcceptedOrderCount(Long maintainerId) {
        QueryWrapper<Task> wrapper = new QueryWrapper<>();
        wrapper.eq("accept_user_id", maintainerId);
        return taskService.count(wrapper);
    }

    /**
     * 获取维修员的评分
     * 这里需要根据实际情况实现，可以从评价表中获取平均分
     */
    private double getMaintainerRating(Long maintainerId) {
        // 这里应该查询评价表，计算平均分
        // 暂时返回一个默认值
        return 5.0;
    }

    // 发布人取消task
    @DeleteMapping("/{id}")
    public Map<String, Object> delTask(@PathVariable Long id) {
        Task task = taskService.getById(id);
        System.out.println(task);
        if (task != null) {
            taskService.removeById(id);
            money.transfer("balance=balance+", task.getReward(), task.getPublish().getStudentId());
        }
        return message.message(true, "取消任务成功", "", null);
    }

    // 接单人取消task
    @PutMapping("/takerCancel/{id}")
    public Map<String, Object> takerCancel(@PathVariable Long id) {

        UpdateWrapper<Task> wrapper = new UpdateWrapper<>();
        wrapper.setSql("accept_user_id=null")
                .setSql("order_time=null")
                .setSql("state=0")
                .eq("id", id);

        boolean update = taskService.update(wrapper);


        if (update) {
            return message.message(true, "取消任务成功", "", null);
        }
        return message.message(false, "取消任务失败", "", null);
    }

    // 接单人接受task
    @PutMapping("/takerAccept")
    @RoleAccess({13})  // 添加学校访问控制
    public Map<String, Object> takerAccept(@RequestBody Map<String, Long> params) {
        try {
            Long taskId = params.get("id");
            Long acceptId = params.get("acceptId");

            if (taskId == null || acceptId == null) {
                return message.message(false, "参数错误", "", null);
            }

            // 先检查任务是否存在且未被接受
            Task task = taskService.getById(taskId);
            if (task == null) {
                return message.message(false, "任务不存在", "", null);
            }

            if (task.getState() != 0) {
                return message.message(false, "任务已被接受", "", null);
            }

            // 检查维修员是否存在
            User maintainer = userService.getById(acceptId);
            if (maintainer == null) {
                return message.message(false, "维修员不存在", "", null);
            }

            // 检查维修员是否有对应的类别和子类别
            if (task.getDeptId() != null && (maintainer.getDeptId() == null || !maintainer.getDeptId().equals(task.getDeptId()))) {
                return message.message(false, "您不能接受此类别的任务", "", null);
            }

            if (task.getTypeId() != null && (maintainer.getClassId() == null || !maintainer.getClassId().equals(task.getTypeId()))) {
                return message.message(false, "您不能接受此子类别的任务", "", null);
            }

            // 使用实体类更新，而不是直接拼接SQL
            Task updateTask = new Task();
            updateTask.setId(taskId);
            updateTask.setAcceptId(acceptId);
            updateTask.setState(1);
            updateTask.setOrderTime(new Date());

            boolean success = taskService.updateById(updateTask);
            if (success) {
                return message.message(true, "接收任务成功", "", null);
            }
            return message.message(false, "接受任务失败", "", null);
        } catch (Exception e) {
            log.error("接受任务失败", e);
            return message.message(false, "接受任务失败：" + e.getMessage(), "", null);
        }
    }

    // 完成task
    @PutMapping("/{id}")
    public Map<String, Object> missionCompleted(@PathVariable Long id) {
        try {
            // 先获取任务信息，确保能获取到接单人信息
            Task task = taskService.getById(id);
            if (task == null) {
                return message.message(false, "任务不存在", "", null);
            }

            if (task.getState() != 1) {
                return message.message(false, "任务状态不正确，无法完成", "", null);
            }

            // 获取接单人ID
            Long acceptUserId = task.getAcceptId();
            if (acceptUserId == null) {
                return message.message(false, "任务未被接受，无法完成", "", null);
            }

            // 获取接单人信息
            User acceptUser = userService.getById(acceptUserId);
            if (acceptUser == null) {
                return message.message(false, "接单人不存在", "", null);
            }

            // 更新任务状态为已完成
            UpdateWrapper<Task> wrapper = new UpdateWrapper<>();
            wrapper.setSql("end_time=now()")
                    .setSql("state=2")
                    .eq("id", id);
            boolean updateSuccess = taskService.update(wrapper);

            if (updateSuccess) {
                // 给维修员转账
                Map<String, Object> transferResult = money.transfer("balance=balance+", task.getReward(), acceptUser.getStudentId());

                log.info("任务完成转账：任务ID={}, 维修员ID={}, 学号={}, 金额={}, 转账结果={}",
                        id, acceptUserId, acceptUser.getStudentId(), task.getReward(), transferResult.get("status"));

                return message.message(true, "任务完成", "", null);
            }
            return message.message(false, "任务完成失败", "", null);
        } catch (Exception e) {
            log.error("完成任务失败", e);
            return message.message(false, "任务完成失败：" + e.getMessage(), "", null);
        }
    }

    // 修改task
    @PutMapping("/edit")
    public Map<String, Object> editTask(Task task, HttpSession session) {

        User user = (User) session.getAttribute("user");

        Task byId = taskService.getById(task.getId());

        //判断是否为自己发布的任务
        if (user.getId().equals(byId.getPublish().getId())) {

            boolean update = taskService.updateById(task);

            if (update) {
                return message.message(true, "编辑任务成功", "", null);
            }
            return message.message(false, "编辑任务失败", "", null);
        }
        return message.message(false, "无权编辑该任务", "", null);
    }

    // 接受任务的接口
    @PutMapping("/accept/{id}")
    @RoleAccess({13})  // 只允许学校ID为13的用户访问
    public Map<String, Object> acceptTask(@PathVariable Long id) {
        // ... 现有代码 ...
        return null; // Placeholder return, actual implementation needed
    }

    // 置顶任务
    @PutMapping("/top/{id}")
    public Map<String, Object> topTask(@PathVariable Long id) {
        Task task = taskService.getById(id);
        if (task != null) {
            task.setIsTop(true);
            boolean success = taskService.updateById(task);
            if (success) {
                return message.message(true, "置顶成功", "", null);
            }
        }
        return message.message(false, "置顶失败", "", null);
    }

    // 取消置顶
    @PutMapping("/cancelTop/{id}")
    public Map<String, Object> cancelTopTask(@PathVariable Long id) {
        Task task = taskService.getById(id);
        if (task != null) {
            task.setIsTop(false);
            boolean success = taskService.updateById(task);
            if (success) {
                return message.message(true, "取消置顶成功", "", null);
            }
        }
        return message.message(false, "取消置顶失败", "", null);
    }

}