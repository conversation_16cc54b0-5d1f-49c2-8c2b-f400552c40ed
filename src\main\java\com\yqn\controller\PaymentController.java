package com.yqn.controller;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yqn.common.tools.MessageTools;
import com.yqn.common.utils.PayUtil;
import com.yqn.pojo.User;
import com.yqn.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 支付相关控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/alipay")
public class PaymentController {
    
    @Autowired
    private PayUtil payUtil;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private MessageTools messageTools;
    
    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    public Map<String, Object> createPayment(@RequestBody Map<String, Object> paymentData) {
        try {
            Long userId = Long.valueOf(paymentData.get("userId").toString());
            Double totalAmount = Double.valueOf(paymentData.get("totalAmount").toString());
            String subject = paymentData.get("subject").toString();
            
            // 验证用户是否存在且为学校用户（role_id = 14）
            User user = userService.getById(userId);
            if (user == null || !user.getRoleId().equals(14L)) {
                return messageTools.message(false, "用户不存在或无充值权限", "", null);
            }
            
            // 验证充值金额
            if (totalAmount <= 0 || totalAmount > 10000) {
                return messageTools.message(false, "充值金额必须在0.01-10000元之间", "", null);
            }
            
            // 创建支付订单
            String payForm = payUtil.createRechargeOrder(userId, totalAmount, subject);
            
            return messageTools.message(true, "创建支付订单成功", "payForm", payForm);
            
        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            return messageTools.message(false, "创建支付订单失败：" + e.getMessage(), "", null);
        }
    }
    
    /**
     * 支付宝异步通知处理
     */
    @PostMapping("/notify")
    public String handleNotify(HttpServletRequest request) {
        log.info("收到支付宝异步通知");
        
        try {
            String result = payUtil.handleAlipayNotify(request);
            
            // 如果支付成功，需要更新用户余额
            if ("success".equals(result)) {
                Map<String, String> params = payUtil.getCallbackParams(request);
                String outTradeNo = params.get("out_trade_no");
                String tradeStatus = params.get("trade_status");
                String totalAmount = params.get("total_amount");
                
                if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                    // 从订单号中提取用户ID
                    String[] parts = outTradeNo.split("_");
                    if (parts.length >= 2) {
                        try {
                            Long userId = Long.valueOf(parts[1]);
                            Double amount = Double.valueOf(totalAmount);
                            
                            // 更新用户余额
                            UpdateWrapper<User> wrapper = new UpdateWrapper<>();
                            wrapper.eq("id", userId)
                                   .eq("role_id", 14L)
                                   .setSql("balance = balance + " + amount);
                            
                            boolean updated = userService.update(wrapper);
                            if (updated) {
                                log.info("用户余额更新成功，用户ID：{}，充值金额：{}", userId, amount);
                            } else {
                                log.error("用户余额更新失败，用户ID：{}，充值金额：{}", userId, amount);
                            }
                        } catch (Exception e) {
                            log.error("更新用户余额异常", e);
                        }
                    }
                }
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("处理支付宝异步通知异常", e);
            return "fail";
        }
    }
    
    /**
     * 支付宝同步返回处理
     */
    @GetMapping("/return")
    public void handleReturn(HttpServletRequest request, HttpServletResponse response) {
        log.info("收到支付宝同步返回");
        
        try {
            // 验证签名
            if (payUtil.verifySignature(request)) {
                Map<String, String> params = payUtil.getCallbackParams(request);
                String outTradeNo = params.get("out_trade_no");
                String tradeNo = params.get("trade_no");
                String totalAmount = params.get("total_amount");
                
                log.info("支付成功，订单号：{}，交易号：{}，金额：{}", outTradeNo, tradeNo, totalAmount);
                
                // 重定向到成功页面
                response.sendRedirect("http://localhost:8848/#/home?payment=success&orderNo=" + outTradeNo);
            } else {
                log.error("支付宝同步返回签名验证失败");
                response.sendRedirect("http://localhost:8848/#/home?payment=fail");
            }
            
        } catch (IOException e) {
            log.error("处理支付宝同步返回异常", e);
        }
    }
    
    /**
     * 查询支付状态
     */
    @GetMapping("/query/{orderNo}")
    public Map<String, Object> queryPaymentStatus(@PathVariable String orderNo) {
        try {
            String result = payUtil.query(orderNo);
            return messageTools.message(true, "查询成功", "result", result);
        } catch (Exception e) {
            log.error("查询支付状态失败", e);
            return messageTools.message(false, "查询失败：" + e.getMessage(), "", null);
        }
    }
}
