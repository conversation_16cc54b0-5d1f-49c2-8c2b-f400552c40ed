-- 创建充值记录表
CREATE TABLE IF NOT EXISTS `recharge_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '支付宝交易号',
  `amount` decimal(10,2) NOT NULL COMMENT '充值金额',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '支付状态：0-待支付，1-支付成功，2-支付失败，3-已取消',
  `payment_method` varchar(20) NOT NULL DEFAULT 'alipay' COMMENT '支付方式：alipay-支付宝',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_trade_no` (`trade_no`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_recharge_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';

-- 插入测试数据（可选）
-- INSERT INTO `recharge_record` (`user_id`, `order_no`, `amount`, `status`, `payment_method`, `create_time`, `remark`) 
-- VALUES 
-- (1, 'RECHARGE_TEST_001', 100.00, 1, 'alipay', NOW(), '测试充值记录'),
-- (2, 'RECHARGE_TEST_002', 50.00, 0, 'alipay', NOW(), '待支付测试记录');
