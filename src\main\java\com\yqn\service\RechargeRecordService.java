package com.yqn.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yqn.pojo.RechargeRecord;

/**
 * 充值记录Service接口
 * <AUTHOR>
 */
public interface RechargeRecordService extends IService<RechargeRecord> {
    
    /**
     * 根据订单号查询充值记录
     * @param orderNo 订单号
     * @return 充值记录
     */
    RechargeRecord getByOrderNo(String orderNo);
    
    /**
     * 更新支付状态
     * @param orderNo 订单号
     * @param status 支付状态
     * @param tradeNo 支付宝交易号
     * @return 是否成功
     */
    boolean updatePaymentStatus(String orderNo, Integer status, String tradeNo);
}
