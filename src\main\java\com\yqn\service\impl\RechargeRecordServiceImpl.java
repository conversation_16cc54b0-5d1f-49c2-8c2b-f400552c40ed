package com.yqn.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yqn.mapper.RechargeRecordMapper;
import com.yqn.pojo.RechargeRecord;
import com.yqn.service.RechargeRecordService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 充值记录Service实现类
 * <AUTHOR>
 */
@Service
public class RechargeRecordServiceImpl extends ServiceImpl<RechargeRecordMapper, RechargeRecord> implements RechargeRecordService {
    
    @Override
    public RechargeRecord getByOrderNo(String orderNo) {
        QueryWrapper<RechargeRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("order_no", orderNo);
        return this.getOne(wrapper);
    }
    
    @Override
    public boolean updatePaymentStatus(String orderNo, Integer status, String tradeNo) {
        UpdateWrapper<RechargeRecord> wrapper = new UpdateWrapper<>();
        wrapper.eq("order_no", orderNo)
               .set("status", status)
               .set("trade_no", tradeNo)
               .set("pay_time", new Date());
        return this.update(wrapper);
    }
}
