package com.yqn.common.tools;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yqn.pojo.User;
import com.yqn.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class PocketMoney {
    @Autowired
    private UserService userService;
    @Autowired
    private MessageTools tools;

    // 零钱通用方法
    public Map<String, Object> transfer(String condition, Double balance, String studentId) {
        try {
            if (balance <= 0) {
                return tools.message(false, "金额必须大于0", "", null);
            }

            UpdateWrapper<User> wrapper = new UpdateWrapper<>();
            wrapper.eq("student_id", studentId)
                  .in("role_id", 13L, 14L)  // 允许维修员(13)和用户(14)进行金额操作
                  .setSql(condition + balance);

            boolean update = userService.update(wrapper);
            if (update) {
                return tools.message(true, "操作成功", "", null);
            }
            return tools.message(false, "操作失败", "", null);
        } catch (Exception e) {
            return tools.message(false, "操作失败：" + e.getMessage(), "", null);
        }
    }

    // 专门为维修员提供的转账方法
    public Map<String, Object> transferToMaintainer(String condition, Double balance, String studentId) {
        try {
            if (balance <= 0) {
                return tools.message(false, "金额必须大于0", "", null);
            }

            UpdateWrapper<User> wrapper = new UpdateWrapper<>();
            wrapper.eq("student_id", studentId)
                  .eq("role_id", 13L)  // 只允许维修员进行操作
                  .setSql(condition + balance);

            boolean update = userService.update(wrapper);
            if (update) {
                return tools.message(true, "维修员余额操作成功", "", null);
            }
            return tools.message(false, "维修员余额操作失败", "", null);
        } catch (Exception e) {
            return tools.message(false, "维修员余额操作失败：" + e.getMessage(), "", null);
        }
    }
}
