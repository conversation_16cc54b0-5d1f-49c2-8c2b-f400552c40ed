package com.yqn.common.tools;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yqn.pojo.User;
import com.yqn.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PocketMoney {
    @Autowired
    private UserService userService;
    @Autowired
    private MessageTools tools;

    // 零钱通用方法
    @Transactional
    public Map<String, Object> transfer(String condition, Double balance, String studentId) {
        try {
            if (balance <= 0) {
                log.warn("转账失败：金额必须大于0，当前金额={}", balance);
                return tools.message(false, "金额必须大于0", "", null);
            }

            // 先查询用户信息
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("student_id", studentId);
            User user = userService.getOne(queryWrapper);

            if (user == null) {
                log.error("转账失败：未找到学号为{}的用户", studentId);
                return tools.message(false, "用户不存在", "", null);
            }

            // 检查角色（13-维修员，14-用户）
            if (user.getRoleId() != 13L && user.getRoleId() != 14L) {
                log.error("转账失败：用户角色不正确，学号={}, 角色ID={}", studentId, user.getRoleId());
                return tools.message(false, "用户角色不正确", "", null);
            }

            log.info("开始转账操作：学号={}, 角色ID={}, 当前余额={}, 操作金额={}, 操作类型={}",
                    studentId, user.getRoleId(), user.getBalance(), balance, condition);

            // 构建SQL更新语句
            String updateSql = condition;
            if (!updateSql.endsWith(" ")) {
                updateSql += " ";
            }
            updateSql += balance;

            UpdateWrapper<User> wrapper = new UpdateWrapper<>();
            wrapper.eq("student_id", studentId)
                  .in("role_id", 13L, 14L)  // 13-维修员, 14-用户
                  .setSql(updateSql);

            boolean update = userService.update(wrapper);

            // 再次查询用户信息，确认余额变化
            User updatedUser = userService.getOne(queryWrapper);
            if (updatedUser != null) {
                log.info("转账操作结果：更新状态={}, 更新前余额={}, 更新后余额={}",
                        update, user.getBalance(), updatedUser.getBalance());
            } else {
                log.error("转账后无法获取用户信息：学号={}", studentId);
            }

            if (update) {
                return tools.message(true, "操作成功", "", null);
            }
            return tools.message(false, "操作失败", "", null);
        } catch (Exception e) {
            log.error("转账操作异常：", e);
            return tools.message(false, "操作失败：" + e.getMessage(), "", null);
        }
    }

    // 专门为维修员提供的转账方法
    @Transactional
    public Map<String, Object> transferToMaintainer(String condition, Double balance, String studentId) {
        try {
            if (balance <= 0) {
                log.warn("维修员转账失败：金额必须大于0，当前金额={}", balance);
                return tools.message(false, "金额必须大于0", "", null);
            }

            // 先查询维修员信息
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("student_id", studentId)
                      .eq("role_id", 13L);  // 13-维修员
            User maintainer = userService.getOne(queryWrapper);

            if (maintainer == null) {
                log.error("维修员转账失败：未找到学号为{}的维修员", studentId);
                return tools.message(false, "维修员不存在", "", null);
            }

            log.info("开始维修员转账操作：学号={}, 当前余额={}, 操作金额={}, 操作类型={}",
                    studentId, maintainer.getBalance(), balance, condition);

            // 构建SQL更新语句
            String updateSql = condition;
            if (!updateSql.endsWith(" ")) {
                updateSql += " ";
            }
            updateSql += balance;

            UpdateWrapper<User> wrapper = new UpdateWrapper<>();
            wrapper.eq("student_id", studentId)
                  .eq("role_id", 13L)  // 13-维修员
                  .setSql(updateSql);

            boolean update = userService.update(wrapper);

            // 再次查询维修员信息，确认余额变化
            User updatedMaintainer = userService.getOne(queryWrapper);
            if (updatedMaintainer != null) {
                log.info("维修员转账操作结果：更新状态={}, 更新前余额={}, 更新后余额={}",
                        update, maintainer.getBalance(), updatedMaintainer.getBalance());
            } else {
                log.error("转账后无法获取维修员信息：学号={}", studentId);
            }

            if (update) {
                return tools.message(true, "维修员余额操作成功", "", null);
            }
            return tools.message(false, "维修员余额操作失败", "", null);
        } catch (Exception e) {
            log.error("维修员转账操作异常：", e);
            return tools.message(false, "维修员余额操作失败：" + e.getMessage(), "", null);
        }
    }
}
