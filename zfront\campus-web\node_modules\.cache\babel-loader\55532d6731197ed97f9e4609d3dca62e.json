{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\src\\router\\index.js", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\router\\index.js", "mtime": 1748627500022}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/ending/250426/zfront/campus-web/src/router/index.js"], "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use", "routes", "path", "component", "redirect", "children", "meta", "title", "name", "router"], "mappings": ";;;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AACA,OAAOC,SAAP,MAAsB,YAAtB;AAEAD,GAAG,CAACE,GAAJ,CAAQD,SAAR;AAEA,IAAME,MAAM,GAAG,CACX;AACIC,EAAAA,IAAI,EAAE,GADV;AAEIC,EAAAA,SAAS,EAAE;AAAA,WAAM,OAAO,cAAP,CAAN;AAAA;AAFf,CADW,EAMX;AACA;AACID,EAAAA,IAAI,EAAE,GADV;AAEIE,EAAAA,QAAQ,EAAE;AAFd,CAPW,EAYX;AACIF,EAAAA,IAAI,EAAE,QADV;AAEIC,EAAAA,SAAS,EAAE;AAAA,WAAM,OAAO,mBAAP,CAAN;AAAA;AAFf,CAZW,EAiBX;AACID,EAAAA,IAAI,EAAE,OADV;AAEIC,EAAAA,SAAS,EAAE;AAAA,WAAM,OAAO,kBAAP,CAAN;AAAA,GAFf;AAGIE,EAAAA,QAAQ,EAAE,CACN;AACIH,IAAAA,IAAI,EAAE,GADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,4BAAP,CAAN;AAAA;AAHf,GADM,EAMN;AACID,IAAAA,IAAI,EAAE,MADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAI,OAAO,2BAAP,CAAJ;AAAA;AAHd,GANM,EAWN;AACID,IAAAA,IAAI,EAAE,WADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAI,OAAO,gCAAP,CAAJ;AAAA;AAHd,GAXM,EAgBN;AACID,IAAAA,IAAI,EAAE,UADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAI,OAAO,+BAAP,CAAJ;AAAA;AAHd,GAhBM,EAqBN;AACID,IAAAA,IAAI,EAAE,QADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAI,OAAO,6BAAP,CAAJ;AAAA;AAHd,GArBM,EA0BN;AACID,IAAAA,IAAI,EAAE,WADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAI,OAAO,gCAAP,CAAJ;AAAA;AAHd,GA1BM,EA+BN;AACID,IAAAA,IAAI,EAAE,SADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAI,OAAO,0BAAP,CAAJ;AAAA;AAHd,GA/BM,EAoCN;AACIK,IAAAA,IAAI,EAAC,UADT;AAEIN,IAAAA,IAAI,EAAC,UAFT;AAGII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAHT;AAIIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,wBAAP,CAAN;AAAA;AAJd,GApCM,EA0CN;AACIK,IAAAA,IAAI,EAAC,YADT;AAEIN,IAAAA,IAAI,EAAC,YAFT;AAGII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAHT;AAIIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,0BAAP,CAAN;AAAA;AAJd,GA1CM,EAgDN;AACA;AACID,IAAAA,IAAI,EAAE,aADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,uBAAP,CAAN;AAAA;AAHf,GAjDM,EAsDN;AACID,IAAAA,IAAI,EAAE,gBADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,yBAAP,CAAN;AAAA;AAHf,GAtDM,EA2DN;AACID,IAAAA,IAAI,EAAE,gBADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,sBAAP,CAAN;AAAA;AAHf,GA3DM,EAgEN;AACID,IAAAA,IAAI,EAAE,cADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,yBAAP,CAAN;AAAA;AAHf,GAhEM,EAqEN;AACID,IAAAA,IAAI,EAAE,gBADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,uBAAP,CAAN;AAAA;AAHf,GArEM,EA2EN;AACID,IAAAA,IAAI,EAAE,qBADV;AAEII,IAAAA,IAAI,EAAE;AAACC,MAAAA,KAAK,EAAE;AAAR,KAFV;AAGIJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,4BAAP,CAAN;AAAA;AAHf,GA3EM;AAHd,CAjBW,EAuGX;AACA;AACID,EAAAA,IAAI,EAAE,QADV;AAEIC,EAAAA,SAAS,EAAE;AAAA,WAAM,OAAO,oBAAP,CAAN;AAAA;AAFf,CAxGW,EA4GX;AACID,EAAAA,IAAI,EAAE,aADV;AAEIC,EAAAA,SAAS,EAAE;AAAA,WAAM,OAAO,mBAAP,CAAN;AAAA,GAFf;AAGIE,EAAAA,QAAQ,EAAC,CACL;AACIH,IAAAA,IAAI,EAAE,GADV;AAEII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAFT;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAI,OAAO,6BAAP,CAAJ;AAAA;AAHd,GADK,EAML;AACIK,IAAAA,IAAI,EAAE,WADV;AAEIN,IAAAA,IAAI,EAAC,MAFT;AAGII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAHT;AAIIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,4BAAP,CAAN;AAAA;AAJd,GANK,EAYL;AACIK,IAAAA,IAAI,EAAC,cADT;AAEIN,IAAAA,IAAI,EAAC,MAFT;AAGII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAHT;AAIIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,4BAAP,CAAN;AAAA;AAJd,GAZK,EAkBL;AACIK,IAAAA,IAAI,EAAC,MADT;AAEIN,IAAAA,IAAI,EAAC,MAFT;AAGII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAHT;AAIIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,4BAAP,CAAN;AAAA;AAJd,GAlBK,EAwBL;AACIK,IAAAA,IAAI,EAAC,QADT;AAEIN,IAAAA,IAAI,EAAC,QAFT;AAGII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAHT;AAIIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,sBAAP,CAAN;AAAA;AAJd,GAxBK,EA8BL;AACIK,IAAAA,IAAI,EAAC,QADT;AAEIN,IAAAA,IAAI,EAAC,QAFT;AAGII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAHT;AAIIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,sBAAP,CAAN;AAAA;AAJd,GA9BK,EAoCL;AACID,IAAAA,IAAI,EAAC,aADT;AAEII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAFT;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,uCAAP,CAAN;AAAA;AAHd,GApCK,EAyCL;AACID,IAAAA,IAAI,EAAC,aADT;AAEII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAFT;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,0CAAP,CAAN;AAAA;AAHd,GAzCK,EA8CL;AACID,IAAAA,IAAI,EAAC,gBADT;AAEII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAFT;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,4CAAP,CAAN;AAAA;AAHd,GA9CK,EAmDL;AACID,IAAAA,IAAI,EAAC,cADT;AAEII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAFT;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,4CAAP,CAAN;AAAA;AAHd,GAnDK,EAwDL;AACID,IAAAA,IAAI,EAAC,gBADT;AAEII,IAAAA,IAAI,EAAC;AAACC,MAAAA,KAAK,EAAC;AAAP,KAFT;AAGIJ,IAAAA,SAAS,EAAC;AAAA,aAAM,OAAO,0CAAP,CAAN;AAAA;AAHd,GAxDK;AAHb,CA5GW,CAAf;AAgLA,IAAMM,MAAM,GAAG,IAAIV,SAAJ,CAAc;AACzBE,EAAAA,MAAM,EAANA;AADyB,CAAd,CAAf;AAIA,eAAeQ,MAAf", "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\n\nVue.use(VueRouter)\n\nconst routes = [\n    {\n        path: \"*\",\n        component: () => import(\"@v/Error.vue\")\n    },\n\n    //用户\n    {\n        path: \"/\",\n        redirect: \"/login\"\n    },\n\n    {\n        path: \"/login\",\n        component: () => import(\"@v/user/Login.vue\")\n    },\n\n    {\n        path: \"/home\",\n        component: () => import(\"@v/user/Home.vue\"),\n        children: [\n            {\n                path: \"/\",\n                meta: {title: '首页'},\n                component: () => import(\"@v/user/children/Index.vue\")\n            },\n            {\n                path: \"task\",\n                meta: {title: \"发布任务\"},\n                component:()=>import('@v/user/children/Task.vue')\n            },\n            {\n                path: \"published\",\n                meta: {title: \"已发布任务\"},\n                component:()=>import('@v/user/children/Published.vue')\n            },\n            {\n                path: \"accepted\",\n                meta: {title: \"已接受任务\"},\n                component:()=>import('@v/user/children/Accepted.vue')\n            },\n            {\n                path: \"accept\",\n                meta: {title: \"已接受任务\"},\n                component:()=>import('@v/user/children/Accept.vue')\n            },\n            {\n                path: \"myProfile\",\n                meta: {title: \"个人信息\"},\n                component:()=>import('@v/user/children/MyProfile.vue')\n            },\n            {\n                path: \"noticeu\",\n                meta: {title: \"查看公告\"},\n                component:()=>import('@v/notice/adviseuser.vue')\n            },\n            {\n                name:\"myremark\",\n                path:\"myremark\",\n                meta:{title:\"我的评价\"},\n                component:() => import(\"@v/remark/myremark.vue\")\n            },\n            {\n                name:\"userremark\",\n                path:\"userremark\",\n                meta:{title:\"我的任务评价\"},\n                component:() => import(\"@v/remark/userremark.vue\")\n            },\n            // 论坛相关路由\n            {\n                path: \"forum/posts\",\n                meta: {title: \"维修员论坛\"},\n                component: () => import(\"@v/forum/PostList.vue\")\n            },\n            {\n                path: \"forum/post/:id\",\n                meta: {title: \"帖子详情\"},\n                component: () => import(\"@v/forum/PostDetail.vue\")\n            },\n            {\n                path: \"forum/my-posts\",\n                meta: {title: \"我的帖子\"},\n                component: () => import(\"@v/forum/MyPosts.vue\")\n            },\n            {\n                path: \"forum/create\",\n                meta: {title: \"发布帖子\"},\n                component: () => import(\"@v/forum/CreatePost.vue\")\n            },\n            {\n                path: \"forum/edit/:id\",\n                meta: {title: \"编辑帖子\"},\n                component: () => import(\"@v/forum/EditPost.vue\")\n            },\n          \n            {\n                path: \"forum/notifications\",\n                meta: {title: \"消息通知\"},\n                component: () => import(\"@v/forum/Notifications.vue\")\n            },\n        ]\n    },\n\n    //管理员\n    {\n        path: \"/admin\",\n        component: () => import(\"@v/admin/Login.vue\"),\n    },\n    {\n        path: \"/admin/home\",\n        component: () => import(\"@v/admin/Home.vue\"),\n        children:[\n            {\n                path: \"/\",\n                meta:{title:\"首页\"},\n                component:()=>import(\"@v/admin/children/Index.vue\")\n            },\n            {\n                name: \"adminRole\",\n                path:\"role\",\n                meta:{title:\"类别信息\"},\n                component:() => import(\"@v/admin/children/Role.vue\")\n            },\n            {\n                name:\"adminStudent\",\n                path:\"user\",\n                meta:{title:\"用户信息\"},\n                component:() => import(\"@v/admin/children/User.vue\")\n            },\n            {\n                name:\"task\",\n                path:\"task\",\n                meta:{title:\"任务信息\"},\n                component:() => import(\"@v/admin/children/Task.vue\")\n            },\n            {\n                name:\"notice\",\n                path:\"notice\",\n                meta:{title:\"公告管理\"},\n                component:() => import(\"@v/notice/advise.vue\")\n            },\n            {\n                name:\"remark\",\n                path:\"remark\",\n                meta:{title:\"评价信息\"},\n                component:() => import(\"@v/remark/remark.vue\")\n            },\n            {\n                path:\"forum/audit\",\n                meta:{title:\"帖子审核\"},\n                component:() => import(\"@v/admin/children/AdminForumAudit.vue\")\n            },\n            {\n                path:\"forum/posts\",\n                meta:{title:\"帖子列表\"},\n                component:() => import(\"@v/admin/children/AdminForumPostList.vue\")\n            },\n            {\n                path:\"forum/post/:id\",\n                meta:{title:\"帖子详情\"},\n                component:() => import(\"@v/admin/children/AdminForumPostDetail.vue\")\n            },\n            {\n                path:\"forum/create\",\n                meta:{title:\"发布帖子\"},\n                component:() => import(\"@v/admin/children/AdminForumPostCreate.vue\")\n            },\n            {\n                path:\"forum/edit/:id\",\n                meta:{title:\"编辑帖子\"},\n                component:() => import(\"@v/admin/children/AdminForumPostEdit.vue\")\n            },\n        ]\n    },\n]\n\nconst router = new VueRouter({\n    routes\n})\n\nexport default router\n"]}]}