{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue", "mtime": 1748631905674}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6QA,SAAA,QAAA,EAAA,YAAA,QAAA,MAAA;AACA,OAAA,IAAA,MAAA,sBAAA;AACA,OAAA,UAAA,MAAA,yBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,OAAA,kCACA,YAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,CADA;AAEA,IAAA,WAFA,uBAEA,GAFA,EAEA;AACA,MAAA,cAAA,CAAA,OAAA,CAAA,YAAA,EAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA;AACA,WAAA,UAAA,GAAA;AAAA,cAAA,GAAA,CAAA,KAAA;AAAA,iBAAA,GAAA,CAAA;AAAA,OAAA;AACA,KALA;AAMA;AACA,IAAA,aAPA,2BAOA;AACA,UAAA,OAAA,GAAA,KAAA,MAAA,CAAA,OAAA;;AACA,UAAA,OAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,QAAA,OAAA,GAAA,CAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA;AAAA,YAAA,KAAA,EAAA;AAAA;AAAA,SAAA,EAAA,MAAA,CAAA,OAAA,CAAA;AACA;;AACA,WAAA,SAAA,GAAA,OAAA;AACA,KAbA;AAcA;AACA,IAAA,WAfA,uBAeA,IAfA,EAeA;AACA;AACA,UAAA,KAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,aAAA,IAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAFA,MAEA;AACA;AACA,QAAA,IAAA;AACA;AACA,KAvBA;AAwBA,IAAA,UAxBA,sBAwBA,QAxBA,EAwBA;AAAA;;AACA,WAAA,KAAA,CAAA,QAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA;AACA,cAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,IAAA,KAAA,CAAA,KAAA,IAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,EAAA;AACA,gBAAA,QAAA,GAAA;AACA,cAAA,EAAA,EAAA,KAAA,CAAA,IAAA,CAAA,EADA;AAEA,cAAA,MAAA,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,IAFA;AAGA;AACA,cAAA,QAAA,EAAA,KAAA,CAAA,QAAA,CAAA,QAJA;AAKA,cAAA,KAAA,EAAA,KAAA,CAAA,QAAA,CAAA,KALA;AAMA,cAAA,GAAA,EAAA,KAAA,CAAA,GANA;AAOA;AACA,cAAA,KAAA,EAAA;AARA,aAAA;;AAWA,YAAA,KAAA,CAAA,IAAA,CAAA,OAAA,EAAA,QAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,gBAAA,KAAA,CAAA,MAAA,GAAA,KAAA;;AACA,gBAAA,KAAA,CAAA,UAAA,CAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA,EAFA,CAGA;;;AACA,oBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,kBAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;AACA,kBAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA;;AACA,gBAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,IAAA,CAAA,EAAA;AACA,eATA,MASA;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,OAAA;AACA;AACA,aAdA,EAeA,KAfA,CAeA,UAAA,GAAA,EAAA;AAAA;;AACA,cAAA,OAAA,CAAA,KAAA,CAAA,gBAAA,EAAA,GAAA;;AACA,cAAA,KAAA,CAAA,IAAA,CAAA,kBAAA,GAAA,CAAA,QAAA,sFAAA,IAAA,0EAAA,GAAA,KAAA,YAAA,EAAA,OAAA;AACA,aAlBA;AAmBA,WA/BA,MA+BA;AACA,YAAA,KAAA,CAAA,UAAA,CAAA,IAAA,EAAA,OAAA,EAAA,OAAA;AACA;AACA,SApCA,MAoCA;AACA,iBAAA,KAAA;AACA;AACA,OAxCA;AAyCA,KAlEA;AAmEA;AACA,IAAA,WApEA,uBAoEA,EApEA,EAoEA;AAAA;;AACA,WAAA,OAAA,CAAA,OAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,SAAA,EAAA,UAHA;AAIA,QAAA,iBAAA,EAAA,KAJA;AAKA,QAAA,YAAA,EAAA,8CALA;AAMA,QAAA,iBAAA,EAAA;AANA,OAAA,EAOA,IAPA,CAOA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AAAA,UAAA,EAAA,EAAA,EAAA;AAAA,UAAA,QAAA,EAAA,GAAA,CAAA;AAAA,SAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA;AACA,SAHA;AAIA,OAbA,EAaA,KAbA,CAaA,YAAA,CACA,CAdA;AAeA,KApFA;AAqFA,IAAA,QArFA,oBAqFA,SArFA,EAqFA;AAAA;;AACA,WAAA,OAAA,CAAA,SAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,YAAA,EAAA,qCAHA;AAIA,QAAA,iBAAA,EAAA;AAJA,OAAA,EAKA,IALA,CAKA,gBAAA;AAAA,YAAA,KAAA,QAAA,KAAA;AACA,YAAA,MAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CADA,CAGA;;AACA,YAAA,MAAA,GAAA,IAAA,IAAA,MAAA,GAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,sBAAA,EAAA,OAAA;;AACA;AACA,SAPA,CASA;;;AACA,YAAA,OAAA,GAAA;AACA,UAAA,OAAA,EAAA,SADA;AAEA,UAAA,WAAA,EAAA,MAFA;AAGA,UAAA,MAAA,EAAA,MAAA,CAAA,IAAA,CAAA;AAHA,SAAA,CAVA,CAgBA;;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,mBAAA,EAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA;AACA,gBAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,EAAA,EAAA,QAAA,EAAA,sBAAA,CAAA;AACA,YAAA,SAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,YAAA,SAAA,CAAA,QAAA,CAAA,KAAA,GAJA,CAMA;;AACA,gBAAA,WAAA,GAAA,WAAA,CAAA,YAAA;AACA,kBAAA,SAAA,CAAA,MAAA,EAAA;AACA,gBAAA,aAAA,CAAA,WAAA,CAAA,CADA,CAEA;;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,MAAA;AACA;AACA,aAPA,EAOA,IAPA,CAAA;AAQA,WAfA,MAeA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA,EAAA,OAAA;AACA;AACA,SApBA,EAqBA,KArBA,CAqBA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,OAAA;AACA,SAxBA;AAyBA,OA/CA,EA+CA,KA/CA,CA+CA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,MAAA;AACA,OAjDA;AAkDA,KAxIA;AAyIA,IAAA,mBAzIA,iCAyIA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,QAAA,CAAA,QAAA,GAAA,KAAA,IAAA,CAAA,QAAA;AACA,WAAA,QAAA,CAAA,KAAA,GAAA,KAAA,IAAA,CAAA,KAAA,CAHA,CAKA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,EAAA;AACA;AACA,YAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,eAAA,QAAA,CAAA,MAAA,GAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA;;AACA,YAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,eAAA,QAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAPA,CASA;;;AACA,YAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,eAAA,aAAA,GAAA,CAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,EAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AACA,SAFA,MAEA,IAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,eAAA,aAAA,GAAA,CAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AACA,SAFA,MAEA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,SAhBA,CAkBA;;;AACA,YAAA,KAAA,IAAA,CAAA,QAAA,IAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,eAAA,aAAA,GAAA,CAAA,KAAA,IAAA,CAAA,QAAA,EAAA,KAAA,IAAA,CAAA,IAAA,EAAA,KAAA,IAAA,CAAA,QAAA,CAAA;AACA,eAAA,QAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA,IAAA,EAAA;AACA,SAHA,MAGA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,QAAA,CAAA,OAAA,GAAA,EAAA;AACA,SAzBA,CA2BA;;;AACA,aAAA,mBAAA;AACA;AACA,KA7KA;AA+KA;AACA,IAAA,mBAhLA,iCAgLA;AAAA;;AACA;AACA,WAAA,IAAA,CAAA,YAAA,EAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,MAAA,CAAA,WAAA,EAHA,CAKA;;AACA,cAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,mBAAA,MAAA,CAAA,IAAA,CAAA,aAAA,EAAA;AAAA,cAAA,MAAA,EAAA,IAAA,CAAA;AAAA,aAAA,EACA,IADA,CACA,UAAA,QAAA,EAAA;AACA,kBAAA,QAAA,CAAA,IAAA,CAAA,MAAA,IAAA,QAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACA;AACA,uDACA,IADA;AAEA,kBAAA,QAAA,EAAA,QAAA,CAAA,IAAA,CAAA;AAFA;AAIA,eAPA,CAQA;;;AACA,qDACA,IADA;AAEA,gBAAA,QAAA,EAAA;AAFA;AAIA,aAdA,EAeA,KAfA,CAeA,YAAA;AACA;AACA,qDACA,IADA;AAEA,gBAAA,QAAA,EAAA;AAFA;AAIA,aArBA,CAAA;AAsBA,WAvBA,CAAA,CANA,CA+BA;;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EACA,IADA,CACA,UAAA,UAAA,EAAA;AACA,YAAA,MAAA,CAAA,eAAA,GAAA,UAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,MAAA,CAAA,eAAA;AACA,WAJA;AAKA,SArCA,MAqCA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,OAAA;AACA;AACA,OA1CA,EA2CA,KA3CA,CA2CA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,OAAA;AACA,OA9CA;AA+CA,KAjOA;AAmOA;AACA,IAAA,eApOA,6BAoOA;AAAA;;AACA,WAAA,IAAA,CAAA,YAAA,EAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,MAAA,CAAA,WAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,OAAA;AACA;AACA,OARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,OAAA;AACA,OAZA;AAaA,KAlPA;AAoPA;AACA,IAAA,iBArPA,6BAqPA,MArPA,EAqPA;AAAA;;AACA,UAAA,CAAA,MAAA,EAAA;AAEA,WAAA,IAAA,CAAA,aAAA,EAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,MAAA,CAAA,YAAA,EAFA,CAIA;;AACA,cAAA,MAAA,GAAA,MAAA,CAAA,YAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,EAAA,KAAA,MAAA,CAAA,QAAA,CAAA,OAAA;AAAA,WAAA,CAAA;;AACA,cAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,GAAA,IAAA;AACA;AACA,SATA,MASA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,GAAA,IAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,OAAA;AACA;AACA,OAhBA,EAiBA,KAjBA,CAiBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,YAAA,EAAA,GAAA;AACA,QAAA,MAAA,CAAA,YAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,iBAAA,EAAA,OAAA;AACA,OAtBA;AAuBA,KA/QA;AAgRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,aAxRA,2BAwRA;AAAA;;AACA;AACA,UAAA,QAAA,GAAA;AACA,QAAA,EAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,QAAA,QAAA,EAAA,KAAA,QAAA,CAAA,QAFA;AAGA,QAAA,KAAA,EAAA,KAAA,QAAA,CAAA,KAHA;AAIA,QAAA,KAAA,EAAA;AAJA,OAAA,CAFA,CASA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,EAAA;AACA;AACA,YAAA,KAAA,aAAA,IAAA,KAAA,aAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,UAAA,QAAA,CAAA,MAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA,CAFA,CAIA;;AACA,cAAA,KAAA,aAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,QAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA;AACA;AACA,SARA,CASA;AATA,aAUA;AACA;AACA,gBAAA,KAAA,QAAA,CAAA,MAAA,EAAA;AACA,cAAA,QAAA,CAAA,MAAA,GAAA,KAAA,QAAA,CAAA,MAAA;AACA,aAJA,CAMA;;;AACA,gBAAA,KAAA,QAAA,CAAA,OAAA,EAAA;AACA,cAAA,QAAA,CAAA,OAAA,GAAA,KAAA,QAAA,CAAA,OAAA;AACA;AACA,WAtBA,CAwBA;;;AACA,YAAA,KAAA,aAAA,IAAA,KAAA,aAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,UAAA,QAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA;AACA,UAAA,QAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA;AACA,UAAA,QAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA;AACA,SA7BA,CA+BA;;;AACA,YAAA,KAAA,QAAA,CAAA,OAAA,EAAA;AACA,UAAA,QAAA,CAAA,OAAA,GAAA,KAAA,QAAA,CAAA,OAAA;AACA;AACA;;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA,EA/CA,CAiDA;;AACA,WAAA,IAAA,CAAA,OAAA,EAAA,QAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA;;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAJA,MAIA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA,EAAA,OAAA;AACA;AACA,OATA,EAUA,KAVA,CAUA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAbA;AAcA,KAxVA;AAyVA;AACA,IAAA,OA1VA,mBA0VA,EA1VA,EA0VA;AAAA;;AACA,WAAA,IAAA,CAAA,WAAA,EAAA,EACA,IADA,CACA,UAAA,EAAA,EAAA;AACA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,GAAA,SAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA,GAAA,cAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA,GAAA,cAAA,EAJA,CAMA;;AACA,cAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,IAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,qBAAA,EADA,CAEA;;AACA,gBAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EADA,CAEA;;AACA,cAAA,MAAA,CAAA,IAAA,CAAA,YAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EACA,IADA,CACA,UAAA,OAAA,EAAA;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA,EADA,CAEA;;AACA,kBAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAHA,CAIA;;AACA,kBAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,eAVA;AAWA;AACA,WAzBA,CA2BA;;;AACA,cAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,GAAA,CAAA,CADA,CAEA;;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,cAAA,EAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EADA;AAEA,cAAA,KAAA,EAAA;AAFA,aAAA;AAIA;;AACA,UAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,EArCA,CAsCA;;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,EAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA;AACA;AACA,OA7CA;AA8CA,KAzYA;AA0YA,IAAA,IA1YA,kBA0YA;AACA,MAAA,cAAA,CAAA,UAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA,KA7YA;AA8YA;AACA,IAAA,UA/YA,sBA+YA,QA/YA,EA+YA;AACA,UAAA,GAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA;;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,EAAA;AACA,aAAA,SAAA,IAAA,GAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA;;AACA,WAAA,SAAA,GAAA,KAAA,SAAA,CAAA,iBAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,KAAA,SAAA;AACA,KAtZA;AAwZA;AACA,IAAA,0BAzZA,wCAyZA;AAAA;;AACA,UAAA,KAAA,IAAA,IAAA,KAAA,IAAA,CAAA,EAAA,EAAA;AACA,aAAA,IAAA,CAAA,kCAAA,EAAA;AAAA,UAAA,MAAA,EAAA,KAAA,IAAA,CAAA;AAAA,SAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA;AACA,SALA,EAMA,KANA,CAMA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,aAAA,EAAA,GAAA;AACA,SARA;AASA;AACA;AAraA,IAFA;AAyaA,EAAA,QAAA,kCACA,QAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,CADA;AAEA,IAAA,KAFA,mBAEA;AACA,aAAA,KAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA;AACA;AAJA,IAzaA;AA+aA,EAAA,IA/aA,kBA+aA;AACA,QAAA,gBAAA,GAAA,SAAA,gBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,KAAA,EAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,QAAA,aAAA,GAAA,SAAA,aAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,KAAA,EAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAQA,WAAA;AACA;AACA,MAAA,SAAA,EAAA,EAFA;AAGA,MAAA,QAAA,EAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA;AALA,OAHA;AAUA;AACA,MAAA,aAAA,EAAA,EAXA;AAYA,MAAA,eAAA,EAAA,EAZA;AAaA;AACA,MAAA,WAAA,EAAA,EAdA;AAeA,MAAA,YAAA,EAAA,EAfA;AAiBA;AACA,MAAA,aAAA,EAAA,EAlBA;AAmBA,MAAA,aAAA,EAAA,EAnBA;AAoBA,MAAA,KAAA,EAAA;AACA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,SAAA,EAAA,gBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,SAAA,EAAA,aAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA,OApBA;AA4BA;AACA,MAAA,UAAA,EAAA;AAAA,cAAA,MAAA;AAAA,iBAAA;AAAA,OA7BA;AA8BA;AACA,MAAA,GAAA,EAAA,GA/BA;AAgCA,MAAA,MAAA,EAAA,KAhCA;AAiCA;AACA,MAAA,SAAA,EAAA,EAlCA;AAmCA;AACA,MAAA,WAAA,EAAA,QAAA,CAAA,eAAA,CAAA,WApCA;AAqCA,MAAA,WAAA,EAAA,GArCA;AAsCA;AACA,MAAA,UAAA,EAAA,KAvCA;AAwCA,MAAA,KAAA,EAAA,EAxCA;AAyCA;AACA,MAAA,IAAA,EAAA,EA1CA;AA2CA;AACA,MAAA,KAAA,EAAA,EA5CA;AA6CA,MAAA,aAAA,EAAA,KA7CA;AA8CA;AACA,MAAA,WAAA,EAAA;AA/CA,KAAA;AAiDA,GAhfA;AAifA,EAAA,KAAA,EAAA;AACA,YADA,kBACA,EADA,EACA,IADA,EACA;AACA,WAAA,aAAA;AACA;AAHA,GAjfA;AAsfA,EAAA,OAtfA,qBAsfA;AAAA;;AACA;AACA,SAAA,aAAA,GAAA,UAAA;AAEA,QAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,YAAA,CAAA,CAAA;;AACA,QAAA,KAAA,EAAA;AACA,WAAA,UAAA,GAAA;AAAA,cAAA,KAAA,CAAA,KAAA;AAAA,iBAAA,KAAA,CAAA;AAAA,OAAA;AACA;;AAEA,QAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA;AACA,WAAA,aAAA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,yBAAA,EAAA,QAAA;AACA,WAAA,OAAA,CAAA,QAAA,EAJA,CAMA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,CAAA,EAAA;AACA,aAAA,0BAAA,GADA,CAGA;;AACA,QAAA,WAAA,CAAA,YAAA;AACA,UAAA,OAAA,CAAA,0BAAA;AACA,SAFA,EAEA,KAFA,CAAA;AAGA,OAdA,CAeA;AACA;AACA;;;AACA,UAAA,KAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,aAAA,IAAA,CAAA,WAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,MAAA,GAAA,IAAA;;AACA,UAAA,OAAA,CAAA,IAAA,CAAA,YAAA,EAAA,SAAA;AACA,SANA;AAOA;AACA,KA3BA,MA2BA;AACA,WAAA,IAAA,CAAA,YAAA,EAAA,OAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA;AAEA,GA/hBA;AAgiBA,EAAA,OAhiBA,qBAgiBA;AAAA;;AACA;AACA,IAAA,MAAA,CAAA,QAAA,GAAA,YAAA;AACA,MAAA,OAAA,CAAA,WAAA,GAAA,QAAA,CAAA,eAAA,CAAA,WAAA;AACA,KAFA,CAFA,CAKA;;;AACA,SAAA,UAAA,CAAA,KAAA,IAAA,CAAA,QAAA;AACA;AAviBA,CAAA", "sourcesContent": ["<template>\n    <div class=\"main\">\n        <div class=\"left\" :style=\"{width:isCollapse?'64px':'200px',background:themeColor.bg,color:themeColor.color}\" style=\"transition: .3s;\">\n            <div class=\"logo\">\n<!--                <img src=\"@s/assets/logo.jpg\" style=\"width: 26%\">-->\n            </div>\n            <el-menu\n                    :collapse-transition=\"false\"\n                    :collapse=\"isCollapse\"\n                    :router=\"true\"\n                    :default-active=\"$route.path\"\n                    :background-color=\"themeColor.bg\"\n                    :text-color=\"themeColor.color\"\n                    :unique-opened=\"true\">\n                <el-menu-item index=\"/home/\">\n                    <i class=\"el-icon-s-home\"></i>\n                    <span>首页</span>\n                </el-menu-item>\n\n                <el-submenu index=\"1\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>任务管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>发布任务</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"2\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-paperclip\"></i>\n                        <span>订单管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已发布任务</span>\n                    </el-menu-item>\n\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"3\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>公告管理</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                        <span>查看公告</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"9\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>评价管理</span>\n                    </template>\n                     <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我发布的评价</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                    <span>查看评价</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <!-- 论坛入口，只对维修员和管理员可见，用户角色ID是14 -->\n                <el-submenu index=\"10\" v-if=\"user.role.id !== 14\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-chat-dot-square\"></i>\n                        <span>维修员论坛</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>/posts\">\n                        <i class=\"el-icon-document\"></i>\n                        <span>帖子列表</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/my-posts\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我的帖子</span>\n                    </el-menu-item>\n                    <!-- 管理员角色不是14（用户角色）也不是13（维修员角色） -->\n                    <el-menu-item v-if=\"user.role.id !== 14 && user.role.id !== 13\" index=\"/home/<USER>/audit\">\n                        <i class=\"el-icon-s-check\"></i>\n                        <span>帖子审核</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/notifications\">\n                        <i class=\"el-icon-bell\"></i>\n                        <span>消息通知</span>\n                        <el-badge v-if=\"unreadCount > 0\" :value=\"unreadCount\" class=\"notification-badge\" />\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"11\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>个人中心</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">个人信息\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-menu-item v-if=\"user.role.id === 14\">\n                    <span>当前余额: {{ user.balance }}元</span>\n                    <!-- <el-button type=\"text\" @click=\"recharge(user.studentId)\">充值余额</el-button> -->\n                </el-menu-item>\n            </el-menu>\n        </div>\n\n        <div class=\"right\"\n             :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px'}\">\n            <div class=\"top\"\n                 :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px',background:themeColor.bg}\">\n                <div class=\"icon\" @click=\"isCollapse = !isCollapse\">\n                    <i :class=\"isCollapse?'el-icon-s-unfold':'el-icon-s-fold'\" :style=\"{color:themeColor.color}\"></i>\n                </div>\n                <el-menu\n\n                        :unique-opened=\"true\"\n                        :default-active=\"activeIndex\"\n                        class=\"el-menu-demo\"\n                        mode=\"horizontal\"\n                        :background-color=\"themeColor.bg\"\n                        :text-color=\"themeColor.color\"\n                        :active-text-color=\"themeColor.color\"\n                        menu-trigger=\"click\">\n\n                    <el-menu-item v-if=\"user.role.id === 14\" @click=\"recharge(user.studentId)\">充值余额</el-menu-item>\n<!--                    <el-menu-item @click=\"recharge(user.studentId)\">充值余额</el-menu-item>-->\n\n                    <el-submenu index=\"1\">\n                        <template slot=\"title\">更换主题</template>\n                        <el-menu-item v-for=\"item in theme\" @click=\"changeColor(item)\">\n                            {{item.name}}\n                        </el-menu-item>\n                    </el-submenu>\n                    <el-submenu index=\"2\">\n<!--                        <template slot=\"title\">{{user.username}}</template>-->\n                        <el-avatar slot=\"title\" style=\"background: #65c4a6; user-select: none;\">{{firstName}}</el-avatar>\n                        <el-menu-item index=\"2-1\" @click=\"exit\">退出</el-menu-item>\n                        <el-menu-item index=\"2-2\" @click=\"updPassword(user.id)\">修改密码</el-menu-item>\n                        <el-menu-item index=\"2-3\" @click=\"personalInformation()\">修改个人信息</el-menu-item>\n                    </el-submenu>\n                </el-menu>\n\n            </div>\n            <div class=\"bottom\">\n                <div class=\"bottom_top\">\n                    <el-breadcrumb separator-class=\"el-icon-arrow-right\">\n                        <el-breadcrumb-item v-for=\"item in breadList\" :to=\"item.path\" v-if=\"item.meta.title\">\n                            {{item.meta.title}}\n                        </el-breadcrumb-item>\n                    </el-breadcrumb>\n                </div>\n                <transition name=\"el-fade-in\" mode=\"out-in\">\n                    <router-view @personalInformation=\"personalInformation\"></router-view>\n                </transition>\n            </div>\n        </div>\n\n        <el-drawer\n                title=\"完善信息\"\n                :visible.sync=\"drawer\"\n                direction=\"rtl\"\n                closeDrawer=\"false\"\n                :show-close=\"false\"\n                :before-close=\"handleClose\">\n            <el-form :model=\"ruleForm\" status-icon :rules=\"rules\" ref=\"ruleForm\" label-width=\"100px\"\n                     class=\"demo-ruleForm ruleform\">\n\n                <!-- 根据角色 ID 动态显示班级信息 -->\n                <el-form-item label=\"类别\" v-if=\"user.role.id !== 14\">\n                    <el-cascader\n                            v-model=\"value\"\n                            :options=\"role.depts\"\n                            :props=\"{\n                children:'classes',\n                label:'name',\n                value:'id'\n            }\"\n                    ></el-cascader>\n                </el-form-item>\n\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"工号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\"></el-input>\n                </el-form-item>\n\n                <el-form-item label=\"性别\">\n                    <el-radio-group v-model=\"sex\">\n                        <el-radio label=\"0\">男</el-radio>\n                        <el-radio label=\"1\">女</el-radio>\n                    </el-radio-group>\n                </el-form-item>\n\n                <el-form-item>\n                    <el-button type=\"primary\" @click=\"submitForm('ruleForm')\">提交</el-button>\n                </el-form-item>\n            </el-form>\n        </el-drawer>\n\n        <el-dialog title=\"修改信息\" :visible.sync=\"dialogVisible\" :close-on-click-modal=\"false\">\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model.number=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"手机号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\" oninput=\"if(value.length>11)value=value.slice(0,11)\"></el-input>\n                </el-form-item>\n\n                <!-- 维修员可以修改类别和子类别 -->\n                <template v-if=\"user.role && user.role.id === 13\">\n                    <el-form-item label=\"类别-子类别\" prop=\"categoryValue\">\n                        <el-cascader\n                            v-model=\"categoryValue\"\n                            :options=\"categoryOptions\"\n                            :props=\"{\n                                children: 'children',\n                                label: 'name',\n                                value: 'id',\n                                checkStrictly: false\n                            }\"\n                            placeholder=\"请选择类别和子类别\"\n                            clearable\n                        ></el-cascader>\n                    </el-form-item>\n\n                    <!-- 维修员地址信息 -->\n                    <el-form-item label=\"省市区\" prop=\"addressRegion\">\n                        <el-cascader\n                            v-model=\"addressRegion\"\n                            :options=\"regionOptions\"\n                            placeholder=\"请选择省/市/区\"\n                            style=\"width: 100%\"\n                        ></el-cascader>\n                    </el-form-item>\n                    <el-form-item label=\"详细地址\" prop=\"address\">\n                        <el-input\n                            v-model=\"ruleForm.address\"\n                            type=\"textarea\"\n                            placeholder=\"请输入详细地址信息，如街道、门牌号等\"\n                            :rows=\"3\"\n                        ></el-input>\n                    </el-form-item>\n                </template>\n\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"dialogVisible = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"submitChanges\">确 定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\n    import {mapState, mapMutations} from \"vuex\"\n    import user from \"@s/store/module/user\";\n    import regionData from '@/assets/data/region.js';\n\n    export default {\n        name: \"Home\",\n        methods: {\n            ...mapMutations('user', ['setUser']),\n            changeColor(val){\n                sessionStorage.setItem(\"themeColor\",JSON.stringify(val))\n                this.themeColor = {'bg':val.value,'color':val.color};\n            },\n            //面包屑\n            getBreadcrumb() {\n                let matched = this.$route.matched;\n                if (matched[0].name != 'home') {\n                    matched = [{path: \"/home/\", meta: {title: '首页'}}].concat(matched)\n                }\n                this.breadList = matched;\n            },\n            //关闭抽屉触发的事件\n            handleClose(done) {\n                // 如果是首次登录且未完善信息，不允许关闭抽屉\n                if (this.user.state === 0) {\n                    this.$msg(\"首次登录必须完善信息\", \"error\")\n                } else {\n                    // 如果不是首次登录或已完善信息，允许关闭抽屉\n                    done()\n                }\n            },\n            submitForm(formName) {\n                this.$refs[formName].validate((valid) => {\n                    if (valid) {\n                        // 根据角色ID决定是否需要选择类别\n                        if ((this.user.role.id !== 14 && this.value) || this.user.role.id === 14) {\n                            const userData = {\n                                id: this.user.id,\n                                deptId: this.user.role.id !== 14 ? this.value[0] : null,\n                                // classId 字段在数据库中不存在，移除该字段\n                                username: this.ruleForm.username,\n                                phone: this.ruleForm.phone,\n                                sex: this.sex,\n                                // 设置状态为已完善信息\n                                state: 1\n                            }\n\n                            this.$put(\"/user\", userData)\n                                .then(res => {\n                                    if (res.data.status) {\n                                        this.drawer = false;\n                                        this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                                        // 更新本地用户信息\n                                        if (res.data.user) {\n                                            sessionStorage.setItem('user', JSON.stringify(res.data.user))\n                                            this.setUser(res.data.user)\n                                        }\n                                        this.newList(this.user.id)\n                                    } else {\n                                        this.$msg(res.data.msg, \"error\")\n                                    }\n                                })\n                                .catch(err => {\n                                    console.error('Update failed:', err)\n                                    this.$msg(err.response?.data?.msg || \"更新失败，请稍后重试\", \"error\")\n                                })\n                        } else {\n                            this.$notifyMsg(\"错误\", \"请选择类别\", \"error\")\n                        }\n                    } else {\n                        return false;\n                    }\n                });\n            },\n            //修改密码\n            updPassword(id) {\n                this.$prompt('请输入密码', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputType: 'password',\n                    closeOnClickModal:false,\n                    inputPattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,\n                    inputErrorMessage: '格式不对，密码只能输入6-16位英文和数字'\n                }).then((res) => {\n                    // console.log(res);\n                    this.$put('/user', {id: id, password: res.value})\n                    .then((res) => {\n                        this.$notifyMsg('成功', res.data.msg, 'success')\n                    })\n                }).catch(() => {\n                })\n            },\n            recharge(studentId) {\n                this.$prompt('请输入充值金额', '充值', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputPattern: /^(0\\.\\d{1,2}|[1-9]\\d*(\\.\\d{1,2})?)$/,\n                    inputErrorMessage: '请输入有效金额（最多两位小数）'\n                }).then(({ value }) => {\n                    const amount = parseFloat(value);\n\n                    // 验证金额范围\n                    if (amount < 0.01 || amount > 10000) {\n                        this.$msg(\"充值金额必须在0.01-10000元之间\", \"error\");\n                        return;\n                    }\n\n                    // 创建支付宝支付订单\n                    const payData = {\n                        subject: '校园帮账户充值',\n                        totalAmount: amount,\n                        userId: this.user.id\n                    };\n\n                    // 调用后端创建支付订单接口\n                    this.$post('api/alipay/create', payData)\n                        .then(res => {\n                            if (res.data.status) {\n                                // 在新窗口中打开支付页面\n                                const payWindow = window.open('', '_blank', 'width=800,height=600');\n                                payWindow.document.write(res.data.payForm);\n                                payWindow.document.close();\n\n                                // 监听支付窗口关闭，刷新用户信息\n                                const checkClosed = setInterval(() => {\n                                    if (payWindow.closed) {\n                                        clearInterval(checkClosed);\n                                        // 刷新用户信息\n                                        this.newList(this.user.id);\n                                        this.$msg(\"请检查支付结果\", \"info\");\n                                    }\n                                }, 1000);\n                            } else {\n                                this.$msg(res.data.msg || \"创建支付订单失败\", \"error\");\n                            }\n                        })\n                        .catch(err => {\n                            console.error('创建支付订单失败:', err);\n                            this.$msg(\"创建支付订单失败，请稍后重试\", \"error\");\n                        });\n                }).catch(() => {\n                    this.$msg(\"已取消充值\", \"info\");\n                });\n            },\n            personalInformation() {\n                this.dialogVisible = true;\n                this.ruleForm.username = this.user.username;\n                this.ruleForm.phone = this.user.phone;\n\n                // 如果是维修员，加载类别和子类别数据\n                if (this.user.role && this.user.role.id === 13) {\n                    // 设置当前的类别和子类别\n                    if (this.user.dept) {\n                        this.ruleForm.deptId = this.user.dept.id;\n                    }\n                    if (this.user.type) {\n                        this.ruleForm.classId = this.user.type.id;\n                    }\n\n                    // 设置级联选择器的初始值\n                    if (this.user.dept && this.user.type) {\n                        this.categoryValue = [this.user.dept.id, this.user.type.id];\n                    } else if (this.user.dept) {\n                        this.categoryValue = [this.user.dept.id];\n                    } else {\n                        this.categoryValue = [];\n                    }\n\n                    // 设置地址选择器的初始值\n                    if (this.user.province && this.user.city && this.user.district) {\n                        this.addressRegion = [this.user.province, this.user.city, this.user.district];\n                        this.ruleForm.address = this.user.address || '';\n                    } else {\n                        this.addressRegion = [];\n                        this.ruleForm.address = '';\n                    }\n\n                    // 加载所有可用的类别和子类别\n                    this.loadCategoryOptions();\n                }\n            },\n\n            // 加载类别和子类别的级联选项\n            loadCategoryOptions() {\n                // 先获取所有类别\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        // 保存原始类别列表（兼容性考虑）\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n\n                        // 为每个类别加载子类别\n                        const promises = res.data.dept.map(dept => {\n                            return this.$get(\"/class/list\", { deptId: dept.id })\n                            .then(classRes => {\n                                if (classRes.data.status && classRes.data.class) {\n                                    // 返回带有子类别的类别对象\n                                    return {\n                                        ...dept,\n                                        children: classRes.data.class\n                                    };\n                                }\n                                // 如果没有子类别，返回原始类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            })\n                            .catch(() => {\n                                // 出错时返回没有子类别的类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            });\n                        });\n\n                        // 等待所有子类别加载完成\n                        Promise.all(promises)\n                        .then(categories => {\n                            this.categoryOptions = categories;\n                            console.log('级联选择器选项:', this.categoryOptions);\n                        });\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadDepartments() {\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadSubCategories(deptId) {\n                if (!deptId) return;\n\n                this.$get(\"/class/list\", { deptId: deptId })\n                .then(res => {\n                    if (res.data.status && res.data.class) {\n                        this.classOptions = res.data.class;\n                        console.log('可用的子类别列表:', this.classOptions);\n\n                        // 如果当前选择的子类别不在新的子类别列表中，清空选择\n                        const exists = this.classOptions.some(item => item.id === this.ruleForm.classId);\n                        if (!exists) {\n                            this.ruleForm.classId = null;\n                        }\n                    } else {\n                        this.classOptions = [];\n                        this.ruleForm.classId = null;\n                        this.$msg(\"获取子类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取子类别列表失败:', err);\n                    this.classOptions = [];\n                    this.ruleForm.classId = null;\n                    this.$msg(\"获取子类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n            // submitChanges(){\n            //     this.$put(\"/user\",{id:this.user.id,username:this.ruleForm.username,phone:this.ruleForm.phone})\n            //     .then(res=>{\n            //         this.$notifyMsg(\"成功\",res.data.msg,\"success\",1000);\n            //         this.dialogVisible = false;\n            //         this.newList(this.user.id)\n            //     })\n            // },\n            submitChanges() {\n                // 准备要更新的用户数据\n                const userData = {\n                    id: this.user.id,\n                    username: this.ruleForm.username,\n                    phone: this.ruleForm.phone,\n                    state: 1\n                };\n\n                // 如果是维修员，添加类别和子类别信息\n                if (this.user.role && this.user.role.id === 13) {\n                    // 从级联选择器中获取类别和子类别ID\n                    if (this.categoryValue && this.categoryValue.length > 0) {\n                        // 第一个值是类别ID\n                        userData.deptId = this.categoryValue[0];\n\n                        // 如果选择了子类别（数组长度大于1），第二个值是子类别ID\n                        if (this.categoryValue.length > 1) {\n                            userData.classId = this.categoryValue[1];\n                        }\n                    }\n                    // 兼容旧版本的选择方式\n                    else {\n                        // 只有当选择了类别时才更新类别ID\n                        if (this.ruleForm.deptId) {\n                            userData.deptId = this.ruleForm.deptId;\n                        }\n\n                        // 只有当选择了子类别时才更新子类别ID\n                        if (this.ruleForm.classId) {\n                            userData.classId = this.ruleForm.classId;\n                        }\n                    }\n\n                    // 处理地址信息\n                    if (this.addressRegion && this.addressRegion.length === 3) {\n                        userData.province = this.addressRegion[0];\n                        userData.city = this.addressRegion[1];\n                        userData.district = this.addressRegion[2];\n                    }\n\n                    // 如果有详细地址，添加到更新数据中\n                    if (this.ruleForm.address) {\n                        userData.address = this.ruleForm.address;\n                    }\n                }\n\n                console.log('提交的用户数据:', userData);\n\n                // 发送更新请求\n                this.$put(\"/user\", userData)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\");\n                            this.dialogVisible = false;\n                            this.newList(this.user.id);\n                        } else {\n                            this.$msg(res.data.msg || \"更新失败\", \"error\");\n                        }\n                    })\n                    .catch(err => {\n                        console.error('更新用户信息失败:', err);\n                        this.$msg(\"更新失败，请稍后重试\", \"error\");\n                    });\n            },\n            //根据当前用户查询id\n            newList(id) {\n                this.$get(\"/user/\" + id)\n                .then((rs) => {\n                    // 确保用户状态正确更新\n                    if (rs.data.user) {\n                        console.log('User data from API:', JSON.stringify(rs.data.user, null, 2));\n                        console.log('User role ID:', rs.data.user.role ? rs.data.user.role.id : 'No role');\n                        console.log('User dept:', rs.data.user.dept ? JSON.stringify(rs.data.user.dept, null, 2) : 'No dept data');\n                        console.log('User type:', rs.data.user.type ? JSON.stringify(rs.data.user.type, null, 2) : 'No type data');\n\n                        // 如果是维修员但没有type信息，尝试重新获取用户数据\n                        if (rs.data.user.role && rs.data.user.role.id === 13 && !rs.data.user.type) {\n                            console.log('维修员没有type信息，尝试修复...');\n                            // 尝试手动设置type信息\n                            if (rs.data.user.classId) {\n                                console.log('找到classId:', rs.data.user.classId);\n                                // 获取type信息\n                                this.$get(\"/class/\" + rs.data.user.classId)\n                                .then(typeRes => {\n                                    if (typeRes.data.class) {\n                                        console.log('获取到type信息:', JSON.stringify(typeRes.data.class, null, 2));\n                                        // 手动设置type信息\n                                        rs.data.user.type = typeRes.data.class;\n                                        // 更新用户信息\n                                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user));\n                                        this.setUser(rs.data.user);\n                                    }\n                                });\n                            }\n                        }\n\n                        // 如果用户已完善信息，确保状态为1\n                        if (rs.data.user.dept && rs.data.user.state === 0) {\n                            rs.data.user.state = 1;\n                            // 更新用户状态\n                            this.$put(\"/user\", {\n                                id: rs.data.user.id,\n                                state: 1\n                            });\n                        }\n                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user))\n                        this.setUser(JSON.parse(sessionStorage.getItem(\"user\")))\n                        // 修改完名字, 清空當前firstName; 避免出現疊加\n                        this.firstName = '';\n                        this.textAvatar(rs.data.user.username);\n                    }\n                })\n            },\n            exit(){\n                sessionStorage.removeItem('user');\n                this.$router.push('/')\n            },\n            // 文字頭像\n            textAvatar(username) {\n                let arr = username.split(' ');\n                for (var i in arr) {\n                    this.firstName += arr[i].substr(0,1);\n                }\n                this.firstName = this.firstName.toLocaleUpperCase();\n                console.log('firstName->' + this.firstName);\n            },\n\n            // 获取未读通知数量\n            getUnreadNotificationCount() {\n                if (this.user && this.user.id) {\n                    this.$get('/forum/notification/unread/count', { userId: this.user.id })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.unreadCount = res.data.count;\n                        }\n                    })\n                    .catch(err => {\n                        console.error('获取未读通知数量失败:', err);\n                    });\n                }\n            }\n        },\n        computed: {\n            ...mapState('user', ['user']),\n            theme(){\n                return this.$store.state.theme.theme\n            },\n        },\n        data() {\n            var validateUsername = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入姓名'));\n                } else {\n                    callback();\n                }\n            };\n            var validatePhone = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入手机号'));\n                } else {\n                    callback();\n                }\n            };\n\n            return {\n                // 文字头像\n                firstName:'',\n                ruleForm: {\n                    username: '',\n                    phone: '',\n                    deptId: null,\n                    classId: null,\n                    address: ''\n                },\n                // 级联选择器的值和选项\n                categoryValue: [],\n                categoryOptions: [],\n                // 保留原有的选项（兼容性考虑）\n                deptOptions: [],\n                classOptions: [],\n\n                // 地址相关\n                addressRegion: [],\n                regionOptions: [],\n                rules: {\n                    username: [\n                        {validator: validateUsername, trigger: 'blur'}\n                    ],\n                    phone: [\n                        {validator: validatePhone, trigger: 'blur'}\n                    ]\n                },\n                //颜色\n                themeColor : {'bg':'#fff','color':'#000'},\n                //性别\n                sex:'0',\n                drawer: false,\n                //当前路由\n                breadList: [],\n                //当前屏幕宽度\n                windowWidth: document.documentElement.clientWidth,\n                activeIndex: '1',\n                //控制菜单是否展开\n                isCollapse: false,\n                admin: \"\",\n                // role: [],\n                role:[],\n                //级联选择器的值\n                value: \"\",\n                dialogVisible:false,\n                // 未读通知数量\n                unreadCount: 0\n            }\n        },\n        watch: {\n            '$route'(to, form) {\n                this.getBreadcrumb()\n            }\n        },\n        created() {\n            // 初始化地址数据\n            this.regionOptions = regionData;\n\n            let theme = JSON.parse(sessionStorage.getItem(\"themeColor\"));\n            if (theme){\n                this.themeColor = {'bg':theme.value,'color':theme.color}\n            }\n\n            if (sessionStorage.getItem('user')){\n                this.getBreadcrumb();\n                const userData = JSON.parse(sessionStorage.getItem(\"user\"));\n                console.log('User data from session:', userData);\n                this.setUser(userData);\n\n                // 如果是维修员或管理员，获取未读通知数量\n                if (this.user.role && (this.user.role.id === 13 || this.user.role.id === 14)) {\n                    this.getUnreadNotificationCount();\n\n                    // 每分钟获取一次未读通知数量\n                    setInterval(() => {\n                        this.getUnreadNotificationCount();\n                    }, 60000);\n                }\n                //\n                // 检查用户是否是首次登录（通过检查state字段）\n                // state为0表示未完善信息，state为1表示已完善信息\n                if (this.user.state === 0) {\n                    this.$get(\"/role/\" + this.user.role.id)\n                    .then((res) => {\n                        console.log(res.data)\n                        this.role = res.data.role;\n                        this.drawer = true\n                        this.$msg(\"首次登录，请完善信息\", \"warning\")\n                    })\n                }\n            }else {\n                this.$msg(\"您向未登陆,没有权限\",\"error\")\n                this.$router.push(\"/\")\n            }\n\n        },\n        mounted() {\n            // 获取当前屏幕宽度\n            window.onresize = () => {\n                this.windowWidth = document.documentElement.clientWidth\n            }\n            // 文字頭像\n            this.textAvatar(this.user.username);\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .main {\n        display: flex;\n        height: 90%;\n\n        .left {\n            position: fixed;\n            height: 100%;\n\n            .logo {\n                width: 90%;\n                /*color: white;*/\n                font-size: 16px;\n                text-align: center;\n                padding: 8px 0;\n                /*border: 1px solid white;*/\n                margin: 9.1px auto 0 auto;\n            }\n        }\n\n        .right {\n            transition: all 0.3s ease 0s;\n            position: relative;\n\n            .top {\n                transition: all 0.3s ease 0s;\n                position: fixed;\n                /*color: #fff;*/\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                z-index: 9;\n\n                .icon {\n                    font-size: 20px;\n                    cursor: pointer;\n                    margin-left: 10px;\n                }\n            }\n\n            .bottom {\n                width: 100%;\n                height: 100%;\n                /*background: #fff;*/\n                margin-top: 65px;\n                .bottom_top {\n                    padding: 20px;\n                }\n            }\n        }\n\n        .ruleform /deep/ .el-input {\n            width: 80% !important;\n        }\n\n        /deep/ .el-cascader {\n            width: 100% !important;\n        }\n    }\n</style>\n"], "sourceRoot": "src/views/user"}]}